# Changelog

## [3.0.0] 2025-13-01

### Upgraded to React 19 ⚡️

## [2.0.0] 2024-07-22

### Vulnerabilities removed

- Most vulnerabilities removed, besides those cause by `react-scripts`. We kept this depedency due to the fact that there are
  many users who still use it, and there is already a Next.js version for thos who want to migrate from `react-scripts`.
- Updated to React 18.
- Updated react-table to Tanstack V8.

## [1.3.0] 2023-05-06

🐛 Bugs solved:

- Sidebar content design bug solved

## [1.2.1] 2022-11-01

🚀 Feature:
-Added TimelineRow

## [1.2.0] 2022-08-23

🚀 HyperTheme Editor

- With the help of the guys from Hyperting, we added HyperTheme Editor. You can check the docs [here](https://www.hyperthe.me/documentation/getting-started/community)!

## [1.1.0] 2022-06-08

🐛 Bugs solved:

- Calendar card - Card border bug on dark mode
- Development Table - Missing content bug
- Solved the warnings regarding stylis-plugin-rtl
- Fixed console warnings

## [1.0.1] 2022-04-25

### Multiple design bugs on mobile solved

- Default - "Daily traffic" card - text align problem on mobile - solved.
- Navbar - Icons - align problem with all icons on mobile - solved.
- Profile - "Your storage" card - "More" icon align problem on mobile - solved.
- Profile - "Complete your profile" card - text align problem on mobile - solved.

## [1.0.0] 2022-04-18

### Original Release

- Added Chakra UI as base framework
