
// Chakra imports
import { Box, SimpleGrid } from "@chakra-ui/react";
import StudentTable from "views/admin/studentList/components/StudentTable";
import React from "react";
import { useAuth } from "contexts/AuthContext";

export default function StudentList() {
  const { token } = useAuth();
  
  // Chakra Color Mode
  return (
    <Box pt={{ base: "130px", md: "80px", xl: "80px" }}>
      <SimpleGrid
        mb='20px'
        columns={{ sm: 1, md: 1 }}
        spacing={{ base: "20px", xl: "20px" }}>
        <StudentTable token={token} />
      </SimpleGrid>
    </Box>
  );
}
