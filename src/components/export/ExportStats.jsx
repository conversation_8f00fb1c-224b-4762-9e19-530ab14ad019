import React, { useState, useEffect } from 'react';
import {
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Box,
  Text,
  useColorModeValue
} from '@chakra-ui/react';
import { useAuth } from 'contexts/AuthContext';

export default function ExportStats() {
  const { token } = useAuth();
  const [stats, setStats] = useState({
    totalExports: 0,
    totalRecords: 0,
    totalSize: 0,
    totalDownloads: 0
  });

  const cardBg = useColorModeValue('white', 'gray.800');

  const fetchStats = async () => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/export/stats`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch export stats:', error);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing="4" mb="6">
      <Stat bg={cardBg} p="4" borderRadius="md" border="1px" borderColor="gray.200">
        <StatLabel>Total Exports</StatLabel>
        <StatNumber>{stats.totalExports}</StatNumber>
        <StatHelpText>Files created</StatHelpText>
      </Stat>
      
      <Stat bg={cardBg} p="4" borderRadius="md" border="1px" borderColor="gray.200">
        <StatLabel>Records Exported</StatLabel>
        <StatNumber>{stats.totalRecords?.toLocaleString()}</StatNumber>
        <StatHelpText>Student records</StatHelpText>
      </Stat>
      
      <Stat bg={cardBg} p="4" borderRadius="md" border="1px" borderColor="gray.200">
        <StatLabel>Storage Used</StatLabel>
        <StatNumber>{formatFileSize(stats.totalSize)}</StatNumber>
        <StatHelpText>Disk space</StatHelpText>
      </Stat>
      
      <Stat bg={cardBg} p="4" borderRadius="md" border="1px" borderColor="gray.200">
        <StatLabel>Downloads</StatLabel>
        <StatNumber>{stats.totalDownloads}</StatNumber>
        <StatHelpText>File downloads</StatHelpText>
      </Stat>
    </SimpleGrid>
  );
}