/* eslint-disable */

import {
  Box,
  Flex,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
  Button,
  HStack,
  VStack,
  IconButton,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  FormControl,
  FormLabel,
  Input,
  Select,
  Badge,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
} from '@chakra-ui/react';
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import Card from 'components/card/Card';
import PhotoUpload from 'components/photoUpload/PhotoUpload';
import ParentPhotoUpload from 'components/photoUpload/ParentPhotoUpload';
import PhotoManager from 'components/photoUpload/PhotoManager';
import ExportButton from 'components/export/ExportButton';
import BackgroundExport from 'components/export/BackgroundExport';
import ExportHistory from 'components/export/ExportHistory';
import ExportStats from 'components/export/ExportStats';
import { FiEdit, FiTrash2, FiEye, FiPlus } from 'react-icons/fi';
import React, { useEffect, useState, useCallback, useRef } from 'react';

const columnHelper = createColumnHelper();

export default function StudentTable({ token }) {
  const [sorting, setSorting] = useState([]);
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState({ current: 1, total: 1, count: 0, totalRecords: 0 });
  const [searchTerm, setSearchTerm] = useState('');
  const [classFilter, setClassFilter] = useState('');
  const [sectionFilter, setSectionFilter] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [newStudentId, setNewStudentId] = useState(null);
  const [formData, setFormData] = useState({
    no: '', roll_number: '', class: '', name: '', section: '', admission_no: '',
    mobile_number: '', dob: '', address: '', gender: '', blood_group: '',
    admission_date: '', academic_year: '2024-25', status: 'Active',
    father_name: '', father_phone: '', mother_name: '', mother_phone: '',
    contact_number: '', email: '', emergency_contact: ''
  });
  const recordsPerPage = 50;
  const toast = useToast();

  const { isOpen: isViewOpen, onOpen: onViewOpen, onClose: onViewClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const { isOpen: isAddOpen, onOpen: onAddOpen, onClose: onAddClose } = useDisclosure();
  const { isOpen: isExportOpen, onOpen: onExportOpen, onClose: onExportClose } = useDisclosure();
  const { isOpen: isHistoryOpen, onOpen: onHistoryOpen, onClose: onHistoryClose } = useDisclosure();

  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');
  const searchInputRef = useRef(null);

  const fetchStudents = async (page = currentPage) => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      params.append('page', page);
      params.append('limit', recordsPerPage);
      
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students?${params}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch students: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const students = Array.isArray(result) ? result : result.students || result.data || [];
      setData(students);
      
      if (result.pagination) {
        setPagination(result.pagination);
      }
      
      console.log('Fetched students:', students.length);
    } catch (err) {
      console.error('Error fetching students:', err);
      setError(err.message);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    columnHelper.accessor('photo', {
      id: 'photo',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">PHOTO</Text>
      ),
      cell: (info) => (
        <PhotoUpload 
          studentId={info.row.original._id}
          currentPhoto={info.getValue()}
          onPhotoUpdate={fetchStudents}
        />
      ),
    }),
    columnHelper.accessor('name', {
      id: 'name',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">NAME</Text>
      ),
      cell: (info) => (
        <Text color={textColor} fontSize="sm" fontWeight="700">
          {info.getValue()}
        </Text>
      ),
    }),
    columnHelper.accessor('class', {
      id: 'class',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">CLASS</Text>
      ),
      cell: (info) => (
        <Text color={textColor} fontSize="sm" fontWeight="700">
          {info.getValue()}
        </Text>
      ),
    }),
    columnHelper.accessor('no', {
      id: 'no',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">STUDENT NO</Text>
      ),
      cell: (info) => (
        <Text color={textColor} fontSize="sm" fontWeight="700">
          {info.getValue()}
        </Text>
      ),
    }),
    columnHelper.accessor('admission_no', {
      id: 'admission_no',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">ADMISSION NO</Text>
      ),
      cell: (info) => (
        <Text color={textColor} fontSize="sm" fontWeight="700">
          {info.getValue()}
        </Text>
      ),
    }),
    columnHelper.accessor('dob', {
      id: 'dob',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">DOB</Text>
      ),
      cell: (info) => (
        <Text color={textColor} fontSize="sm" fontWeight="700">
          {info.getValue() ? new Date(info.getValue()).toLocaleDateString() : ''}
        </Text>
      ),
    }),
    columnHelper.accessor('roll_number', {
      id: 'roll_number',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">ROLL NO</Text>
      ),
      cell: (info) => (
        <Text color={textColor} fontSize="sm" fontWeight="700">
          {info.getValue()}
        </Text>
      ),
    }),
    columnHelper.accessor('section', {
      id: 'section',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">SECTION</Text>
      ),
      cell: (info) => (
        <Badge colorScheme="blue" variant="subtle">
          {info.row.original.class} {info.getValue()}
        </Badge>
      ),
    }),
    columnHelper.accessor('mobile_number', {
      id: 'mobile_number',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">MOBILE</Text>
      ),
      cell: (info) => (
        <Text color={textColor} fontSize="sm" fontWeight="700">
          {info.getValue()}
        </Text>
      ),
    }),

    columnHelper.accessor('actions', {
      id: 'actions',
      header: () => (
        <Text fontSize={{ sm: '10px', lg: '12px' }} color="gray.400">ACTIONS</Text>
      ),
      cell: (info) => (
        <HStack spacing="2">
          <IconButton
            size="sm"
            icon={<FiEye />}
            colorScheme="blue"
            variant="outline"
            onClick={() => handleView(info.row.original)}
          />
          <IconButton
            size="sm"
            icon={<FiEdit />}
            colorScheme="green"
            variant="outline"
            onClick={() => handleEdit(info.row.original)}
          />
          <IconButton
            size="sm"
            icon={<FiTrash2 />}
            colorScheme="red"
            variant="outline"
            onClick={() => handleDelete(info.row.original)}
          />
        </HStack>
      ),
    }),
  ];

  const handleView = (student) => {
    setSelectedStudent(student);
    onViewOpen();
  };

  const handleEdit = (student) => {
    setSelectedStudent(student);
    setFormData({
      no: student.no || '',
      roll_number: student.roll_number || '',
      class: student.class || '',
      name: student.name || '',
      section: student.section || '',
      admission_no: student.admission_no || '',
      mobile_number: student.mobile_number || '',
      dob: student.dob ? student.dob.split('T')[0] : '',
      address: student.address || '',
      gender: student.gender || '',
      blood_group: student.blood_group || '',
      admission_date: student.admission_date ? student.admission_date.split('T')[0] : '',
      academic_year: student.academic_year || '2024-25',
      status: student.status || 'Active',
      father_name: student.father_name || '',
      father_phone: student.father_phone || '',
      mother_name: student.mother_name || '',
      mother_phone: student.mother_phone || '',
      contact_number: student.contact_number || '',
      email: student.email || '',
      emergency_contact: student.emergency_contact || ''
    });
    onEditOpen();
  };

  const handleAdd = () => {
    setFormData({
      no: '', roll_number: '', class: '', name: '', section: '', admission_no: '',
      mobile_number: '', dob: '', address: '', gender: '', blood_group: '',
      admission_date: '', academic_year: '2024-25', status: 'Active',
      father_name: '', father_phone: '', mother_name: '', mother_phone: '',
      contact_number: '', email: '', emergency_contact: ''
    });
    setNewStudentId(null);
    onAddOpen();
  };

  const handleSave = async () => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const url = selectedStudent 
        ? `${baseUrl}/api/students/${selectedStudent._id}` 
        : `${baseUrl}/api/students/register`;
      const method = selectedStudent ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (response.ok) {
        const result = await response.json();
        
        if (!selectedStudent) {
          setNewStudentId(result._id);
          toast({ 
            title: 'Student Created', 
            description: 'Student created successfully. You can now upload photos.', 
            status: 'success', 
            duration: 3000 
          });
        } else {
          toast({ 
            title: 'Success', 
            description: 'Student updated successfully', 
            status: 'success', 
            duration: 3000 
          });
          onEditClose();
        }
        
        fetchStudents();
        
        if (selectedStudent) {
          onEditClose();
          onAddClose();
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save student');
      }
    } catch (error) {
      console.error('Save error:', error);
      toast({ title: 'Error', description: error.message || 'Failed to save student', status: 'error', duration: 3000 });
    }
  };

  const handleDelete = async (student) => {
    if (window.confirm(`Are you sure you want to delete ${student.name}?`)) {
      try {
        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
        const response = await fetch(`${baseUrl}/api/students/${student._id}`, {
          method: 'DELETE',
          headers: { Authorization: `Bearer ${token}` }
        });
        if (response.ok) {
          toast({ title: 'Success', description: 'Student deleted successfully', status: 'success', duration: 3000 });
          fetchStudents();
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to delete student');
        }
      } catch (error) {
        console.error('Delete error:', error);
        toast({ title: 'Error', description: error.message || 'Failed to delete student', status: 'error', duration: 3000 });
      }
    }
  };

  useEffect(() => {
    if (token) {
      fetchStudents(currentPage);
    }
  }, [token, currentPage]);

  // Client-side filtering for search
  const filteredData = data.filter(student => {
    const matchesSearch = !searchTerm || 
      student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.admission_no?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.roll_number?.toString().toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.class?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = !classFilter || student.class === classFilter;
    const matchesSection = !sectionFilter || student.section === sectionFilter;
    return matchesSearch && matchesClass && matchesSection;
  });

  // Use server pagination when no filters, client pagination when filtering
  const isFiltering = searchTerm || classFilter || sectionFilter;
  const displayData = isFiltering ? filteredData : data;
  const totalPages = isFiltering ? Math.ceil(filteredData.length / recordsPerPage) : (pagination.total || 1);

  const table = useReactTable({
    data: displayData,
    columns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    debugTable: true,
  });

  if (loading) {
    return (
      <Card w="100%" px="0px">
        <Flex justifyContent="center" align="center" h="100px">
          <Spinner size="xl" />
        </Flex>
      </Card>
    );
  }

  if (error) {
    return (
      <Card w="100%" px="0px">
        <Alert status="error" borderRadius="md" mb="4">
          <AlertIcon />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </Card>
    );
  }



  return (
    <>
      <Box w="100%" bg="white" p="4">
        {/* Header */}
        <Flex justifyContent="space-between" align="center" mb="4">
          <VStack align="start" spacing="0">
            <Text fontSize="20px" fontWeight="bold" color="gray.800">
              Students
            </Text>
            <Text fontSize="xs" color="gray.500">
              👥 Total: {pagination.totalRecords || data.length}
            </Text>
          </VStack>
          <HStack spacing="2">
            <ExportButton 
              filters={{
                ...(searchTerm && { search: searchTerm }),
                ...(classFilter && { class: classFilter }),
                ...(sectionFilter && { section: sectionFilter })
              }}
            />
            <Button 
              variant="outline" 
              size="sm" 
              fontSize="xs"
              onClick={onExportOpen}
            >
              🔄 Large Export
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              fontSize="xs"
              onClick={onHistoryOpen}
            >
              📁 History
            </Button>
            <Button leftIcon={<FiPlus />} colorScheme="blue" size="sm" fontSize="xs" onClick={handleAdd}>
              Add student
            </Button>
          </HStack>
        </Flex>

        {/* Search */}
        <Box mb="4">
          <Input
            ref={searchInputRef}
            placeholder="Search students by name, class, admission no, or roll no..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="sm"
            maxW="400px"
          />
        </Box>

        {/* Filters */}
        <HStack spacing="3" mb="4">
          <Select 
            placeholder="Classes" 
            size="sm" 
            maxW="100px" 
            fontSize="xs"
            value={classFilter}
            onChange={(e) => setClassFilter(e.target.value)}
          >
            <option value="10th">10th</option>
            <option value="11th">11th</option>
            <option value="12th">12th</option>
          </Select>
          <Select 
            placeholder="Section" 
            size="sm" 
            maxW="100px" 
            fontSize="xs"
            value={sectionFilter}
            onChange={(e) => setSectionFilter(e.target.value)}
          >
            <option value="A">A</option>
            <option value="B">B</option>
            <option value="C">C</option>
          </Select>
          <Select placeholder="Age" size="sm" maxW="80px" fontSize="xs">
            <option value="15">15</option>
            <option value="16">16</option>
            <option value="17">17</option>
          </Select>
          <Button variant="outline" size="sm" fontSize="xs">
            ⚙️ All filters
          </Button>
          <HStack spacing="1" ml="auto">
            <IconButton size="sm" variant="outline" icon={<Text>📋</Text>} />
            <IconButton size="sm" variant="outline" icon={<Text>📊</Text>} />
            <IconButton size="sm" variant="outline" icon={<Text>📈</Text>} />
            <IconButton size="sm" variant="outline" icon={<Text>🗑️</Text>} />
          </HStack>
        </HStack>

        {/* Table */}
        <Box overflowX="auto" overflowY="auto" maxH="60vh" position="relative">
          {loading && (
            <Flex position="absolute" top="50%" left="50%" transform="translate(-50%, -50%)" zIndex="10">
              <Spinner size="lg" />
            </Flex>
          )}
          <Table variant="simple" size="sm" opacity={loading ? 0.5 : 1}>
            <Thead bg="gray.50" position="sticky" top="0" zIndex="10">
              <Tr>
                <Th w="40px" textAlign="center" minW="40px" py="2" fontSize="10px" color="gray.500" fontWeight="600">
                  <input type="checkbox" size="sm" />
                </Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600" minW="60px" py="2">ID</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600" minW="180px" py="2">STUDENT</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600" minW="80px" py="2">GENDER</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600" minW="100px" py="2">FATHER</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600" minW="80px" py="2">CLASS</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600" minW="100px" py="2">ACTIONS</Th>
              </Tr>
            </Thead>
            <Tbody>
              {displayData.map((student, index) => {
                  return (
                    <Tr key={student._id} _hover={{ bg: "gray.50" }} bg="white" py="1">
                      <Td textAlign="center" py="2">
                        <input type="checkbox" size="sm" />
                      </Td>
                      <Td py="2">
                        <Text fontSize="sm" fontWeight="medium" color="gray.700">
                          {student.no || (447 + index)}
                        </Text>
                      </Td>
                      <Td py="2">
                        <HStack spacing="2">
                          <PhotoUpload 
                            studentId={student._id}
                            currentPhoto={student.photo}
                            onPhotoUpdate={fetchStudents}
                            showUpload={!student.photo}
                            studentName={student.name}
                          />
                          <VStack align="start" spacing="0">
                            <Text fontWeight="medium" fontSize="sm" color="gray.800">
                              {student.name}
                            </Text>
                            <HStack spacing="1">
                              {student.photo && <Badge colorScheme="green" size="xs">📷</Badge>}
                              {student.father_photo && <Badge colorScheme="blue" size="xs">👨</Badge>}
                              {student.mother_photo && <Badge colorScheme="pink" size="xs">👩</Badge>}
                              {!student.photo && !student.father_photo && !student.mother_photo && (
                                <Badge colorScheme="gray" size="xs">No Photos</Badge>
                              )}
                            </HStack>
                          </VStack>
                        </HStack>
                      </Td>
                      <Td py="2">
                        <Text fontSize="sm" color="gray.600">
                          {student.gender || 'Male'}
                        </Text>
                      </Td>
                      <Td py="2">
                        <Text fontSize="sm" color="gray.600">
                          {student.father_name || 'N/A'}
                        </Text>
                      </Td>
                      <Td py="2">
                        <Text fontSize="sm" fontWeight="medium" color="gray.700">
                          {student.class}
                        </Text>
                      </Td>
                      <Td py="2">
                        <HStack spacing="1">
                          <IconButton 
                            size="xs" 
                            variant="ghost" 
                            icon={<FiEye />} 
                            onClick={() => handleView(student)}
                          />
                          <IconButton 
                            size="xs" 
                            variant="ghost" 
                            icon={<FiEdit />} 
                            onClick={() => handleEdit(student)}
                          />
                          <Menu>
                            <MenuButton
                              as={IconButton}
                              size="xs"
                              variant="ghost"
                              icon={<Text>⋯</Text>}
                            />
                            <MenuList fontSize="sm">
                              <MenuItem icon={<Text>📞</Text>} onClick={() => window.open(`tel:${student.mobile_number}`)}>
                                Call
                              </MenuItem>
                              <MenuItem icon={<Text>✉️</Text>} onClick={() => window.open(`mailto:${student.email || ''}`)}>
                                Email
                              </MenuItem>
                              <MenuItem icon={<Text>📄</Text>}>
                                Export
                              </MenuItem>
                              <MenuItem icon={<Text>📊</Text>}>
                                Analytics
                              </MenuItem>
                              <MenuItem icon={<FiTrash2 />} color="red.500" onClick={() => handleDelete(student)}>
                                Delete
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </HStack>
                      </Td>
                    </Tr>
                  );
                })}
            </Tbody>
          </Table>
        </Box>

        {/* Pagination */}
        <Flex justifyContent="space-between" alignItems="center" mt="3" pt="3" borderTop="1px" borderColor="gray.100">
          <Text fontSize="xs" color="gray.500">
            {isFiltering ? `1 to ${filteredData.length} of ${filteredData.length}` : `${((currentPage - 1) * recordsPerPage) + 1} to ${Math.min(currentPage * recordsPerPage, pagination.totalRecords || data.length)} of ${pagination.totalRecords || data.length}`}
          </Text>
          <HStack spacing="1">
            <IconButton size="xs" variant="ghost" icon={<Text>←</Text>} onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))} isDisabled={currentPage === 1} />
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => i + 1).map(page => (
              <Button 
                key={page}
                size="xs" 
                colorScheme={currentPage === page ? "blue" : "gray"} 
                variant={currentPage === page ? "solid" : "ghost"} 
                minW="6"
                onClick={() => setCurrentPage(page)}
              >
                {page}
              </Button>
            ))}
            <IconButton size="xs" variant="ghost" icon={<Text>→</Text>} onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))} isDisabled={currentPage >= totalPages} />
          </HStack>
        </Flex>
      </Box>

      {/* View Modal */}
      <Modal isOpen={isViewOpen} onClose={onViewClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Student Details</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedStudent && (
              <VStack spacing="4" align="stretch">
                <HStack>
                  <Text fontWeight="bold">Student No:</Text>
                  <Text>{selectedStudent.no}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Name:</Text>
                  <Text>{selectedStudent.name}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Admission No:</Text>
                  <Text>{selectedStudent.admission_no}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Class:</Text>
                  <Text>{selectedStudent.class} {selectedStudent.section}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Roll No:</Text>
                  <Text>{selectedStudent.roll_number}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Mobile:</Text>
                  <Text>{selectedStudent.mobile_number}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">DOB:</Text>
                  <Text>{selectedStudent.dob ? new Date(selectedStudent.dob).toLocaleDateString() : ''}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Gender:</Text>
                  <Text>{selectedStudent.gender}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Blood Group:</Text>
                  <Text>{selectedStudent.blood_group}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Father Name:</Text>
                  <Text>{selectedStudent.father_name}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Father Phone:</Text>
                  <Text>{selectedStudent.father_phone}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Mother Name:</Text>
                  <Text>{selectedStudent.mother_name}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Mother Phone:</Text>
                  <Text>{selectedStudent.mother_phone}</Text>
                </HStack>
                <HStack>
                  <Text fontWeight="bold">Emergency Contact:</Text>
                  <Text>{selectedStudent.emergency_contact}</Text>
                </HStack>
                <Box>
                  <Text fontWeight="bold" mb="3">Photo Management:</Text>
                  <PhotoManager
                    studentId={selectedStudent._id}
                    studentData={selectedStudent}
                    onPhotoUpdate={fetchStudents}
                  />
                </Box>

              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onViewClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Edit/Add Modal */}
      <Modal isOpen={isEditOpen || isAddOpen} onClose={() => { onEditClose(); onAddClose(); }} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{selectedStudent ? 'Edit Student' : 'Add Student'}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing="6">
              {/* Basic Information Section */}
              <Box w="full" p="4" border="1px" borderColor="gray.200" borderRadius="md">
                <Text fontSize="md" fontWeight="bold" mb="3" color="blue.600">📋 Basic Information</Text>
                <VStack spacing="3">
                  <HStack w="full">
                    <FormControl>
                      <FormLabel fontSize="sm">Name * (e.g., Dhimahi Patel)</FormLabel>
                      <Input value={formData.name} onChange={(e) => setFormData({...formData, name: e.target.value})} />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Gender * (e.g., Male/Female)</FormLabel>
                      <Select value={formData.gender} onChange={(e) => setFormData({...formData, gender: e.target.value})} placeholder="Select Gender">
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                      </Select>
                    </FormControl>
                  </HStack>
                  <HStack w="full">
                    <FormControl>
                      <FormLabel fontSize="sm">Date of Birth * (e.g., 2015-08-15)</FormLabel>
                      <Input type="date" value={formData.dob} onChange={(e) => setFormData({...formData, dob: e.target.value})} />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Blood Group (e.g., A+, B+)</FormLabel>
                      <Select value={formData.blood_group} onChange={(e) => setFormData({...formData, blood_group: e.target.value})} placeholder="Select Blood Group">
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                      </Select>
                    </FormControl>
                  </HStack>
                  <FormControl>
                    <FormLabel fontSize="sm">Address * (e.g., Valsad, Gujarat)</FormLabel>
                    <Input value={formData.address} onChange={(e) => setFormData({...formData, address: e.target.value})} />
                  </FormControl>
                </VStack>
              </Box>

              {/* Academic Information Section */}
              <Box w="full" p="4" border="1px" borderColor="gray.200" borderRadius="md">
                <Text fontSize="md" fontWeight="bold" mb="3" color="green.600">🎓 Academic Information</Text>
                <VStack spacing="3">
                  <HStack w="full">
                    <FormControl>
                      <FormLabel fontSize="sm">Student No * (e.g., 100)</FormLabel>
                      <Input value={formData.no} onChange={(e) => setFormData({...formData, no: e.target.value})} />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Roll Number * (e.g., 100)</FormLabel>
                      <Input value={formData.roll_number} onChange={(e) => setFormData({...formData, roll_number: e.target.value})} />
                    </FormControl>
                  </HStack>
                  <HStack w="full">
                    <FormControl>
                      <FormLabel fontSize="sm">Class * (e.g., 10th)</FormLabel>
                      <Select value={formData.class} onChange={(e) => setFormData({...formData, class: e.target.value})}>
                        <option value="">Select Class</option>
                        <option value="10th">10th</option>
                        <option value="11th">11th</option>
                        <option value="12th">12th</option>
                      </Select>
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Section * (e.g., A)</FormLabel>
                      <Input value={formData.section} onChange={(e) => setFormData({...formData, section: e.target.value})} />
                    </FormControl>
                  </HStack>
                  <HStack w="full">
                    <FormControl>
                      <FormLabel fontSize="sm">Admission No * (e.g., SS100)</FormLabel>
                      <Input value={formData.admission_no} onChange={(e) => setFormData({...formData, admission_no: e.target.value})} />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Admission Date * (e.g., 2022-02-10)</FormLabel>
                      <Input type="date" value={formData.admission_date} onChange={(e) => setFormData({...formData, admission_date: e.target.value})} />
                    </FormControl>
                  </HStack>
                </VStack>
              </Box>

              {/* Parent Information Section */}
              <Box w="full" p="4" border="1px" borderColor="gray.200" borderRadius="md">
                <Text fontSize="md" fontWeight="bold" mb="3" color="purple.600">👨‍👩‍👧‍👦 Parent Information</Text>
                <VStack spacing="3">
                  <HStack w="full">
                    <FormControl>
                      <FormLabel fontSize="sm">Father Name * (e.g., Hemant Patel)</FormLabel>
                      <Input value={formData.father_name} onChange={(e) => setFormData({...formData, father_name: e.target.value})} />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Father Phone * (e.g., 9033595789)</FormLabel>
                      <Input value={formData.father_phone} onChange={(e) => setFormData({...formData, father_phone: e.target.value})} />
                    </FormControl>
                  </HStack>
                  <HStack w="full">
                    <FormControl>
                      <FormLabel fontSize="sm">Mother Name * (e.g., Artiben Patel)</FormLabel>
                      <Input value={formData.mother_name} onChange={(e) => setFormData({...formData, mother_name: e.target.value})} />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Emergency Contact * (e.g., 9033595789)</FormLabel>
                      <Input value={formData.emergency_contact} onChange={(e) => setFormData({...formData, emergency_contact: e.target.value})} />
                    </FormControl>
                  </HStack>
                </VStack>
              </Box>

              {/* Contact Information Section */}
              <Box w="full" p="4" border="1px" borderColor="gray.200" borderRadius="md">
                <Text fontSize="md" fontWeight="bold" mb="3" color="orange.600">📞 Contact Information</Text>
                <VStack spacing="3">
                  <HStack w="full">
                    <FormControl>
                      <FormLabel fontSize="sm">Mobile Number * (e.g., 9033595789)</FormLabel>
                      <Input value={formData.mobile_number} onChange={(e) => setFormData({...formData, mobile_number: e.target.value})} />
                    </FormControl>
                    <FormControl>
                      <FormLabel fontSize="sm">Email (e.g., <EMAIL>)</FormLabel>
                      <Input value={formData.email} onChange={(e) => setFormData({...formData, email: e.target.value})} />
                    </FormControl>
                  </HStack>
                </VStack>
              </Box>
              
              {/* Photo Management Section */}
              <Box w="full" p="4" border="1px" borderColor="gray.200" borderRadius="md">
                <Text fontSize="md" fontWeight="bold" mb="3" color="pink.600">📷 Photo Management</Text>
                {selectedStudent ? (
                  <PhotoManager
                    studentId={selectedStudent._id}
                    studentData={selectedStudent}
                    onPhotoUpdate={fetchStudents}
                  />
                ) : newStudentId ? (
                  <PhotoManager
                    studentId={newStudentId}
                    studentData={{
                      name: formData.name,
                      father_name: formData.father_name,
                      mother_name: formData.mother_name
                    }}
                    onPhotoUpdate={fetchStudents}
                  />
                ) : (
                  <PhotoManager
                    studentId={null}
                    studentData={{
                      name: formData.name || 'Student Name',
                      father_name: formData.father_name || 'Father Name',
                      mother_name: formData.mother_name || 'Mother Name',
                      photo: null,
                      father_photo: null,
                      mother_photo: null
                    }}
                    onPhotoUpdate={() => {}}
                  />
                )}
              </Box>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={() => { onEditClose(); onAddClose(); }}>Cancel</Button>
            {!newStudentId && (
              <Button colorScheme="blue" onClick={handleSave}>{selectedStudent ? 'Update' : 'Create'}</Button>
            )}
            {newStudentId && (
              <Button colorScheme="green" onClick={() => { onAddClose(); setNewStudentId(null); }}>Done</Button>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>
      
      {/* Background Export Modal */}
      <BackgroundExport 
        isOpen={isExportOpen} 
        onClose={onExportClose}
        filters={{
          ...(searchTerm && { search: searchTerm }),
          ...(classFilter && { class: classFilter }),
          ...(sectionFilter && { section: sectionFilter })
        }}
      />
      
      {/* Export History Modal */}
      <Modal isOpen={isHistoryOpen} onClose={onHistoryClose} size="6xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Export History</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <ExportStats />
            <ExportHistory />
          </ModalBody>
          <ModalFooter>
            <Button onClick={onHistoryClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
