import React, { useState } from 'react';
import {
  Box,
  Button,
  Image,
  Input,
  VStack,
  HStack,
  Text,
  useToast,
  Progress,
  Avatar,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Badge,
  SimpleGrid,
  Card,
  CardBody,
  Heading,
  Divider,
  useColorModeValue
} from '@chakra-ui/react';
import { FiCamera, FiUpload, FiUser, FiUsers } from 'react-icons/fi';
import { useAuth } from 'contexts/AuthContext';

export default function PhotoManager({ 
  studentId, 
  studentData,
  onPhotoUpdate 
}) {
  const { token } = useAuth();
  const toast = useToast();
  
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [uploadType, setUploadType] = useState(''); // 'student', 'father', 'mother'
  const { isOpen, onOpen, onClose } = useDisclosure();

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const getInitials = (name) => {
    if (!name) return 'N/A';
    const names = name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  const getImageUrl = (photoPath) => {
    if (!photoPath) return null;
    if (photoPath.startsWith('http')) return photoPath;
    
    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    const filename = photoPath.includes('/') ? photoPath.split('/').pop() : photoPath;
    return `${baseUrl}/api/students/photo/${filename}?t=${Date.now()}`;
  };

  const validateFile = (file) => {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    
    if (file.size > maxSize) {
      throw new Error('File size must be less than 5MB');
    }
    
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      throw new Error('Only JPG, JPEG, PNG, GIF, and WEBP files are allowed');
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      validateFile(file);
      setSelectedFile(file);
      setPreview(URL.createObjectURL(file));
    } catch (error) {
      toast({
        title: 'Invalid File',
        description: error.message,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const openUploadModal = (type) => {
    setUploadType(type);
    setSelectedFile(null);
    setPreview(null);
    onOpen();
  };

  const uploadPhoto = async () => {
    if (!selectedFile || !uploadType) return;

    const formData = new FormData();
    formData.append('photo', selectedFile);

    try {
      setUploading(true);
      setProgress(20);

      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      let endpoint;
      
      switch (uploadType) {
        case 'student':
          endpoint = 'upload-photo';
          break;
        case 'father':
          endpoint = 'upload-father-photo';
          break;
        case 'mother':
          endpoint = 'upload-mother-photo';
          break;
        default:
          throw new Error('Invalid upload type');
      }

      const response = await fetch(`${baseUrl}/api/students/${studentId}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      setProgress(80);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      setProgress(100);

      if (data.success) {
        toast({
          title: 'Success',
          description: data.message || `${uploadType} photo uploaded successfully`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        
        if (onPhotoUpdate) {
          onPhotoUpdate();
        }
        
        onClose();
        setSelectedFile(null);
        setPreview(null);
        setUploadType('');
      } else {
        throw new Error(data.message || 'Upload failed');
      }
    } catch (error) {
      console.error(`${uploadType} photo upload error:`, error);
      toast({
        title: 'Upload Failed',
        description: error.message || `Failed to upload ${uploadType} photo`,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  const getUploadTypeLabel = () => {
    switch (uploadType) {
      case 'student':
        return 'Student';
      case 'father':
        return 'Father';
      case 'mother':
        return 'Mother';
      default:
        return '';
    }
  };

  const getUploadTypeColor = () => {
    switch (uploadType) {
      case 'student':
        return 'blue';
      case 'father':
        return 'green';
      case 'mother':
        return 'pink';
      default:
        return 'gray';
    }
  };

  return (
    <>
      <Card bg={cardBg} border="1px" borderColor={borderColor}>
        <CardBody>
          <VStack spacing="6">
            <Heading size="md" textAlign="center">
              <HStack justify="center" spacing="2">
                <FiCamera />
                <Text>Photo Management</Text>
              </HStack>
            </Heading>
            
            <Divider />

            <SimpleGrid columns={{ base: 1, md: 3 }} spacing="6" w="full">
              {/* Student Photo */}
              <VStack spacing="3">
                <Box position="relative">
                  <Avatar
                    size="xl"
                    src={getImageUrl(studentData?.photo)}
                    name={getInitials(studentData?.name)}
                    bg="blue.500"
                    color="white"
                  />
                  <IconButton
                    icon={<FiCamera />}
                    size="sm"
                    colorScheme="blue"
                    borderRadius="full"
                    position="absolute"
                    bottom="0"
                    right="0"
                    onClick={() => openUploadModal('student')}
                  />
                </Box>
                <VStack spacing="1">
                  <Badge colorScheme="blue" fontSize="sm">
                    <HStack spacing="1">
                      <FiUser size="12" />
                      <Text>Student</Text>
                    </HStack>
                  </Badge>
                  <Text fontSize="sm" fontWeight="medium" textAlign="center">
                    {studentData?.name || 'Student Name'}
                  </Text>
                </VStack>
              </VStack>

              {/* Father Photo */}
              <VStack spacing="3">
                <Box position="relative">
                  <Avatar
                    size="xl"
                    src={getImageUrl(studentData?.father_photo)}
                    name={getInitials(studentData?.father_name)}
                    bg="green.500"
                    color="white"
                  />
                  <IconButton
                    icon={<FiCamera />}
                    size="sm"
                    colorScheme="green"
                    borderRadius="full"
                    position="absolute"
                    bottom="0"
                    right="0"
                    onClick={() => openUploadModal('father')}
                  />
                </Box>
                <VStack spacing="1">
                  <Badge colorScheme="green" fontSize="sm">
                    <HStack spacing="1">
                      <FiUsers size="12" />
                      <Text>Father</Text>
                    </HStack>
                  </Badge>
                  <Text fontSize="sm" fontWeight="medium" textAlign="center">
                    {studentData?.father_name || 'Father Name'}
                  </Text>
                </VStack>
              </VStack>

              {/* Mother Photo */}
              <VStack spacing="3">
                <Box position="relative">
                  <Avatar
                    size="xl"
                    src={getImageUrl(studentData?.mother_photo)}
                    name={getInitials(studentData?.mother_name)}
                    bg="pink.500"
                    color="white"
                  />
                  <IconButton
                    icon={<FiCamera />}
                    size="sm"
                    colorScheme="pink"
                    borderRadius="full"
                    position="absolute"
                    bottom="0"
                    right="0"
                    onClick={() => openUploadModal('mother')}
                  />
                </Box>
                <VStack spacing="1">
                  <Badge colorScheme="pink" fontSize="sm">
                    <HStack spacing="1">
                      <FiUsers size="12" />
                      <Text>Mother</Text>
                    </HStack>
                  </Badge>
                  <Text fontSize="sm" fontWeight="medium" textAlign="center">
                    {studentData?.mother_name || 'Mother Name'}
                  </Text>
                </VStack>
              </VStack>
            </SimpleGrid>

            <Text fontSize="xs" color="gray.500" textAlign="center">
              Click the camera icon to upload or change photos
            </Text>
          </VStack>
        </CardBody>
      </Card>

      {/* Upload Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="md">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            Upload {getUploadTypeLabel()} Photo
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb="6">
            <VStack spacing="4">
              {preview && (
                <Image
                  src={preview}
                  alt="Preview"
                  boxSize="200px"
                  objectFit="cover"
                  borderRadius="lg"
                  border="2px solid"
                  borderColor={`${getUploadTypeColor()}.200`}
                />
              )}
              
              <Input
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                onChange={handleFileSelect}
                display="none"
                id="photo-upload-input"
              />
              
              <Button
                as="label"
                htmlFor="photo-upload-input"
                leftIcon={<FiUpload />}
                colorScheme="gray"
                variant="outline"
                cursor="pointer"
                w="full"
              >
                Choose Photo
              </Button>

              <Text fontSize="xs" color="gray.500" textAlign="center">
                Supported formats: JPG, JPEG, PNG, GIF, WEBP (Max: 5MB)
              </Text>

              {uploading && (
                <Box w="full">
                  <Text fontSize="sm" mb="2">Uploading {getUploadTypeLabel()} photo...</Text>
                  <Progress 
                    value={progress} 
                    colorScheme={getUploadTypeColor()}
                    borderRadius="md"
                  />
                </Box>
              )}

              <Button
                colorScheme={getUploadTypeColor()}
                onClick={uploadPhoto}
                isDisabled={!selectedFile || uploading}
                isLoading={uploading}
                w="full"
                size="lg"
              >
                Upload {getUploadTypeLabel()} Photo
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}