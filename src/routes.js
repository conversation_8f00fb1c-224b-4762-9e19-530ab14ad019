import React from 'react';

import { Icon } from '@chakra-ui/react';
import {
  MdBarChart,
  MdPerson,
  MdHome,
  MdLock,
  MdOutlineShoppingCart,
} from 'react-icons/md';

// Admin Imports
import MainDashboard from 'views/admin/default';
import NFTMarketplace from 'views/admin/marketplace';
import Profile from 'views/admin/profile';
import DataTables from 'views/admin/dataTables';
import RTL from 'views/admin/rtl';
import StudentList from 'views/admin/studentList';
import StudentRegistration from 'views/admin/studentRegistration';
import Payment from 'views/admin/payment';
import PaymentHistory from 'views/admin/paymentHistory';
import EMIPayment from 'views/admin/emiPayment';
import EMIDashboard from 'views/admin/emiDashboard';
import StudentResults from 'views/admin/studentResults';
import PasswordManagement from 'views/admin/passwordManagement';

// Auth Imports
import SignInCentered from 'views/auth/signIn';

const routes = [
  {
    name: 'Main Dashboard',
    layout: '/admin',
    path: '/default',
    icon: <Icon as={MdHome} width="20px" height="20px" color="inherit" />,
    component: <MainDashboard />,
  },

  {
    name: 'Student List',
    layout: '/admin',
    icon: <Icon as={MdPerson} width="20px" height="20px" color="inherit" />,
    path: '/student-list',
    component: <StudentList />,
  },
  {
    name: 'Student Registration',
    layout: '/admin',
    icon: <Icon as={MdPerson} width="20px" height="20px" color="inherit" />,
    path: '/student-registration',
    component: <StudentRegistration />,
    hidden: true,
  },
  {
    name: 'Payment',
    layout: '/admin',
    icon: <Icon as={MdOutlineShoppingCart} width="20px" height="20px" color="inherit" />,
    path: '/payment',
    component: <Payment />,
  },
  {
    name: 'Payment History',
    layout: '/admin',
    icon: <Icon as={MdBarChart} width="20px" height="20px" color="inherit" />,
    path: '/payment-history',
    component: <PaymentHistory />,
  },
  {
    name: 'EMI Payment',
    layout: '/admin',
    icon: <Icon as={MdOutlineShoppingCart} width="20px" height="20px" color="inherit" />,
    path: '/emi-payment',
    component: <EMIPayment />,
  },
  {
    name: 'EMI Dashboard',
    layout: '/admin',
    icon: <Icon as={MdBarChart} width="20px" height="20px" color="inherit" />,
    path: '/emi-dashboard',
    component: <EMIDashboard />,
  },
  {
    name: 'Student Results',
    layout: '/admin',
    icon: <Icon as={MdBarChart} width="20px" height="20px" color="inherit" />,
    path: '/student-results',
    component: <StudentResults />,
  },
  {
    name: 'Password Management',
    layout: '/admin',
    icon: <Icon as={MdLock} width="20px" height="20px" color="inherit" />,
    path: '/password-management',
    component: <PasswordManagement />,
  },
  {
    name: 'NFT Marketplace',
    layout: '/admin',
    path: '/nft-marketplace',
    icon: (
      <Icon
        as={MdOutlineShoppingCart}
        width="20px"
        height="20px"
        color="inherit"
      />
    ),
    component: <NFTMarketplace />,
    secondary: true,
  },
  {
    name: 'Data Tables',
    layout: '/admin',
    icon: <Icon as={MdBarChart} width="20px" height="20px" color="inherit" />,
    path: '/data-tables',
    component: <DataTables />,
  },
  {
    name: 'Profile',
    layout: '/admin',
    path: '/profile',
    icon: <Icon as={MdPerson} width="20px" height="20px" color="inherit" />,
    component: <Profile />,
  },
  {
    name: 'Sign In',
    layout: '/auth',
    path: '/sign-in',
    icon: <Icon as={MdLock} width="20px" height="20px" color="inherit" />,
    component: <SignInCentered />,
    hidden: true,
  },

];

export default routes;
