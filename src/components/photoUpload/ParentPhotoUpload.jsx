import React, { useState } from 'react';
import {
  Box,
  Button,
  Image,
  Input,
  VStack,
  Text,
  useToast,
  Progress,
  Avatar,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  HStack,
  Badge
} from '@chakra-ui/react';
import { FiCamera, FiUpload } from 'react-icons/fi';
import { useAuth } from 'contexts/AuthContext';

export default function ParentPhotoUpload({ 
  studentId, 
  currentFatherPhoto, 
  currentMotherPhoto, 
  onPhotoUpdate, 
  fatherName = "Father", 
  motherName = "Mother" 
}) {
  const { token } = useAuth();
  
  const getInitials = (name) => {
    const names = name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  const getImageUrl = (photoPath) => {
    if (!photoPath) return null;
    if (photoPath.startsWith('http')) return photoPath;
    
    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    const filename = photoPath.includes('/') ? photoPath.split('/').pop() : photoPath;
    return `${baseUrl}/api/students/photo/${filename}?t=${Date.now()}`;
  };

  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [uploadType, setUploadType] = useState(''); // 'father' or 'mother'
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const validateFile = (file) => {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    
    if (file.size > maxSize) {
      throw new Error('File size must be less than 5MB');
    }
    
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      throw new Error('Only JPG, JPEG, PNG, GIF, and WEBP files are allowed');
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      validateFile(file);
      setSelectedFile(file);
      setPreview(URL.createObjectURL(file));
    } catch (error) {
      toast({
        title: 'Invalid File',
        description: error.message,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const openUploadModal = (type) => {
    setUploadType(type);
    setSelectedFile(null);
    setPreview(null);
    onOpen();
  };

  const uploadPhoto = async () => {
    if (!selectedFile || !uploadType) return;

    const formData = new FormData();
    formData.append('photo', selectedFile);

    try {
      setUploading(true);
      setProgress(20);

      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const endpoint = uploadType === 'father' ? 'upload-father-photo' : 'upload-mother-photo';
      const response = await fetch(`${baseUrl}/api/students/${studentId}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      setProgress(80);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      setProgress(100);

      if (data.success) {
        toast({
          title: 'Success',
          description: data.message || `${uploadType === 'father' ? 'Father' : 'Mother'} photo uploaded successfully`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        
        if (onPhotoUpdate) {
          onPhotoUpdate();
        }
        
        // Force component re-render by clearing and setting preview
        setTimeout(() => {
          if (onPhotoUpdate) onPhotoUpdate();
        }, 100);
        
        onClose();
        setSelectedFile(null);
        setPreview(null);
        setUploadType('');
      } else {
        throw new Error(data.message || 'Upload failed');
      }
    } catch (error) {
      console.error(`${uploadType} photo upload error:`, error);
      toast({
        title: 'Upload Failed',
        description: error.message || `Failed to upload ${uploadType} photo`,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  return (
    <>
      <HStack spacing="4">
        <VStack spacing="2">
          <Box position="relative" display="inline-block">
            <Avatar
              size="md"
              src={getImageUrl(currentFatherPhoto)}
              name={getInitials(fatherName)}
              bg="blue.500"
              color="white"
            />
            <IconButton
              icon={<FiCamera />}
              size="xs"
              colorScheme="blue"
              borderRadius="full"
              position="absolute"
              bottom="-2px"
              right="-2px"
              onClick={() => openUploadModal('father')}
            />
          </Box>
          <Badge colorScheme="blue" fontSize="xs">Father</Badge>
        </VStack>

        <VStack spacing="2">
          <Box position="relative" display="inline-block">
            <Avatar
              size="md"
              src={getImageUrl(currentMotherPhoto)}
              name={getInitials(motherName)}
              bg="pink.500"
              color="white"
            />
            <IconButton
              icon={<FiCamera />}
              size="xs"
              colorScheme="pink"
              borderRadius="full"
              position="absolute"
              bottom="-2px"
              right="-2px"
              onClick={() => openUploadModal('mother')}
            />
          </Box>
          <Badge colorScheme="pink" fontSize="xs">Mother</Badge>
        </VStack>
      </HStack>

      <Modal isOpen={isOpen} onClose={onClose} size="md">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            Upload {uploadType === 'father' ? 'Father' : 'Mother'} Photo
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb="6">
            <VStack spacing="4">
              {preview && (
                <Image
                  src={preview}
                  alt="Preview"
                  boxSize="200px"
                  objectFit="cover"
                  borderRadius="lg"
                />
              )}
              
              <Input
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                onChange={handleFileSelect}
                display="none"
                id="parent-photo-upload"
              />
              
              <Button
                as="label"
                htmlFor="parent-photo-upload"
                leftIcon={<FiUpload />}
                colorScheme="gray"
                variant="outline"
                cursor="pointer"
              >
                Choose Photo
              </Button>

              {uploading && (
                <Box w="full">
                  <Text fontSize="sm" mb="2">Uploading...</Text>
                  <Progress value={progress} colorScheme="blue" />
                </Box>
              )}

              <Button
                colorScheme={uploadType === 'father' ? 'blue' : 'pink'}
                onClick={uploadPhoto}
                isDisabled={!selectedFile || uploading}
                isLoading={uploading}
                w="full"
              >
                Upload {uploadType === 'father' ? 'Father' : 'Mother'} Photo
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}