import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useColorModeValue,
  Spinner,
  Input,
  Button,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Select,
  Textarea,
  NumberInput,
  NumberInputField,
  VStack,
  HStack,
  IconButton,
} from '@chakra-ui/react';
import { AddIcon, DeleteIcon } from '@chakra-ui/icons';
import Card from 'components/card/Card';
import { useAuth } from 'contexts/AuthContext';

export default function PaymentHistory() {
  const { token } = useAuth();
  const [payments, setPayments] = useState([]);
  const [filteredPayments, setFilteredPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [deletePaymentId, setDeletePaymentId] = useState(null);
  const [editingPayment, setEditingPayment] = useState(null);
  const [editFormData, setEditFormData] = useState({
    totalAmount: 0,
    paymentType: 'tuition',
    description: '',
    installments: [0],
    dueDates: ['']
  });
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const cancelRef = React.useRef();
  const toast = useToast();

  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');

  useEffect(() => {
    fetchPayments();
  }, []);

  useEffect(() => {
    if (searchTerm) {
      const filtered = payments.filter(payment =>
        (payment.student_id?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (payment.payment_type || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredPayments(filtered);
    } else {
      setFilteredPayments(payments);
    }
  }, [searchTerm, payments]);

  const fetchPayments = async () => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/payments`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      console.log('API Response:', data);
      const paymentsData = Array.isArray(data) ? data : (data.payments || []);
      setPayments(paymentsData);
      setFilteredPayments(paymentsData);
    } catch (error) {
      console.error('Error fetching payments:', error);
      setPayments([]);
      setFilteredPayments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleEditPayment = (payment) => {
    setEditingPayment(payment);
    // Handle installments properly - extract amounts from installment objects
    const installments = payment.installments && Array.isArray(payment.installments) && payment.installments.length > 0
      ? payment.installments.map(inst => inst.amount || inst)
      : [payment.total_amount || 0];
    
    // Handle due dates - extract from installment objects
    const dueDates = payment.installments && Array.isArray(payment.installments) && payment.installments.length > 0
      ? payment.installments.map(inst => inst.due_date ? new Date(inst.due_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0])
      : installments.map(() => new Date().toISOString().split('T')[0]);
    
    setEditFormData({
      totalAmount: payment.total_amount || 0,
      paymentType: payment.payment_type || 'tuition',
      description: payment.description || '',
      installments: installments,
      dueDates: dueDates
    });
    onEditOpen();
  };

  const handleUpdatePayment = async () => {
    // Validate installments
    const installmentsTotal = editFormData.installments.reduce((sum, amount) => sum + (amount || 0), 0);
    if (installmentsTotal !== editFormData.totalAmount) {
      toast({
        title: 'Validation Error',
        description: `Installments total (₹${installmentsTotal}) must equal total amount (₹${editFormData.totalAmount})`,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    // Validate that all installments have amounts > 0
    if (editFormData.installments.some(amount => !amount || amount <= 0)) {
      toast({
        title: 'Validation Error',
        description: 'All installments must have amounts greater than 0',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/payments/${editingPayment._id || editingPayment.id}`, {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          totalAmount: editFormData.totalAmount,
          paymentType: editFormData.paymentType,
          description: editFormData.description,
          installments: editFormData.installments,
          dueDates: editFormData.dueDates
        }),
      });

      const result = await response.json();
      
      if (result.success || response.ok) {
        toast({
          title: 'Payment Updated',
          description: 'Payment has been updated successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        fetchPayments();
        onEditClose();
      } else {
        throw new Error(result.error || 'Update failed');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update payment',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const addInstallment = () => {
    setEditFormData(prev => {
      const updated = {
        ...prev,
        installments: [...prev.installments, 0],
        dueDates: [...prev.dueDates, new Date().toISOString().split('T')[0]]
      };
      return updated;
    });
  };

  const removeInstallment = (index) => {
    setEditFormData(prev => {
      const updated = {
        ...prev,
        installments: prev.installments.filter((_, i) => i !== index),
        dueDates: prev.dueDates.filter((_, i) => i !== index)
      };
      // Recalculate total amount
      updated.totalAmount = updated.installments.reduce((sum, amount) => sum + (amount || 0), 0);
      return updated;
    });
  };

  const updateInstallment = (index, field, value) => {
    setEditFormData(prev => {
      const updated = {
        ...prev,
        [field]: prev[field].map((item, i) => i === index ? value : item)
      };
      
      // Auto-update total amount when installments change
      if (field === 'installments') {
        updated.totalAmount = updated.installments.reduce((sum, amount) => sum + (amount || 0), 0);
      }
      
      return updated;
    });
  };

  const handleDeletePayment = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/payments/${deletePaymentId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast({
          title: 'Payment Deleted',
          description: 'Payment record has been deleted successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        fetchPayments();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete payment',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
      onDeleteClose();
      setDeletePaymentId(null);
    }
  };

  if (loading) {
    return (
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
        <Flex justifyContent="center" align="center" h="200px">
          <Spinner size="xl" />
        </Flex>
      </Box>
    );
  }

  return (
    <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
      <Card w="100%" px="0px" overflowX={{ sm: 'scroll', lg: 'hidden' }}>
        <Flex px="25px" mb="8px" justifyContent="space-between" align="center">
          <Text color={textColor} fontSize="22px" fontWeight="700">
            Payment History
          </Text>
          <Input
            placeholder="Search by student name or payment type"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            maxW="300px"
          />
        </Flex>
        
        <Table variant="simple" color="gray.500" mb="24px" mt="12px">
          <Thead>
            <Tr>
              <Th borderColor={borderColor}>Student Name</Th>
              <Th borderColor={borderColor}>Class</Th>
              <Th borderColor={borderColor}>Payment Type</Th>
              <Th borderColor={borderColor}>Amount</Th>
              <Th borderColor={borderColor}>Installments</Th>
              <Th borderColor={borderColor}>Date</Th>
              <Th borderColor={borderColor}>Description</Th>
              <Th borderColor={borderColor}>Actions</Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredPayments.map((payment, index) => (
              <Tr key={index}>
                <Td borderColor="transparent">
                  <Text color={textColor} fontSize="sm" fontWeight="700">
                    {payment.student_id?.name || 'N/A'}
                  </Text>
                </Td>
                <Td borderColor="transparent">
                  <Text color={textColor} fontSize="sm" fontWeight="700">
                    {payment.student_id?.class || 'N/A'}
                  </Text>
                </Td>
                <Td borderColor="transparent">
                  <Text color={textColor} fontSize="sm" fontWeight="700">
                    {payment.payment_type || 'N/A'}
                  </Text>
                </Td>
                <Td borderColor="transparent">
                  <Text color={textColor} fontSize="sm" fontWeight="700">
                    ₹{payment.total_amount || payment.amount || 0}
                  </Text>
                </Td>
                <Td borderColor="transparent">
                  <VStack align="start" spacing={1}>
                    {payment.installments && Array.isArray(payment.installments) && payment.installments.length > 0 ? (
                      payment.installments.map((installment, idx) => (
                        <HStack key={idx} spacing={2}>
                          <Text color={textColor} fontSize="xs" fontWeight="bold">
                            EMI {installment.installment_number || idx + 1}:
                          </Text>
                          <Text color={textColor} fontSize="xs">
                            ₹{installment.amount || installment}
                          </Text>
                          <Text 
                            fontSize="xs" 
                            color={installment.status === 'paid' ? 'green.500' : installment.status === 'overdue' ? 'red.500' : 'orange.500'}
                            fontWeight="bold"
                          >
                            {installment.status || 'pending'}
                          </Text>
                        </HStack>
                      ))
                    ) : (
                      <Text color={textColor} fontSize="xs">
                        Single Payment: ₹{payment.total_amount || 0}
                      </Text>
                    )}
                    {payment.emiSummary && (
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        {payment.emiSummary.paid_installments}/{payment.emiSummary.total_installments} paid
                      </Text>
                    )}
                  </VStack>
                </Td>
                <Td borderColor="transparent">
                  <Text color={textColor} fontSize="sm" fontWeight="700">
                    {payment.created_at ? new Date(payment.created_at).toLocaleDateString() : 'N/A'}
                  </Text>
                </Td>
                <Td borderColor="transparent">
                  <Text color={textColor} fontSize="sm" fontWeight="700">
                    {payment.description || '-'}
                  </Text>
                </Td>
                <Td borderColor="transparent">
                  <HStack spacing={2}>
                    <Button
                      size="sm"
                      colorScheme="blue"
                      onClick={() => handleEditPayment(payment)}
                    >
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      colorScheme="red"
                      onClick={() => {
                        setDeletePaymentId(payment._id || payment.id);
                        onDeleteOpen();
                      }}
                    >
                      Delete
                    </Button>
                  </HStack>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Card>
      
      {/* Edit Payment Modal */}
      <Modal isOpen={isEditOpen} onClose={onEditClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Edit Payment</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl>
                <FormLabel>Total Amount (₹)</FormLabel>
                <NumberInput
                  value={editFormData.totalAmount}
                  onChange={(value) => setEditFormData(prev => ({...prev, totalAmount: parseInt(value) || 0}))}
                >
                  <NumberInputField />
                </NumberInput>
              </FormControl>
              
              <FormControl>
                <FormLabel>Payment Type</FormLabel>
                <Select
                  value={editFormData.paymentType}
                  onChange={(e) => setEditFormData(prev => ({...prev, paymentType: e.target.value}))}
                >
                  <option value="tuition">Tuition</option>
                  <option value="exam">Exam</option>
                  <option value="transport">Transport</option>
                  <option value="library">Library</option>
                  <option value="other">Other</option>
                </Select>
              </FormControl>
              
              <FormControl>
                <FormLabel>Description</FormLabel>
                <Textarea
                  value={editFormData.description}
                  onChange={(e) => setEditFormData(prev => ({...prev, description: e.target.value}))}
                  rows={3}
                />
              </FormControl>
              
              <FormControl>
                <FormLabel>Installments ({editFormData.installments.length} installment{editFormData.installments.length !== 1 ? 's' : ''})</FormLabel>
                <VStack spacing={2}>
                  {editFormData.installments.map((amount, index) => (
                    <HStack key={index} w="100%" p={2} border="1px solid" borderColor="gray.200" borderRadius="md">
                      <Text minW="80px" fontSize="sm" fontWeight="bold">
                        EMI {index + 1}:
                      </Text>
                      <NumberInput
                        flex={1}
                        value={amount}
                        onChange={(value) => updateInstallment(index, 'installments', parseInt(value) || 0)}
                        min={0}
                      >
                        <NumberInputField placeholder="Amount (₹)" />
                      </NumberInput>
                      <Input
                        flex={1}
                        type="date"
                        value={editFormData.dueDates[index] || ''}
                        onChange={(e) => updateInstallment(index, 'dueDates', e.target.value)}
                      />
                      {editFormData.installments.length > 1 && (
                        <IconButton
                          icon={<DeleteIcon />}
                          colorScheme="red"
                          size="sm"
                          onClick={() => removeInstallment(index)}
                          aria-label={`Remove installment ${index + 1}`}
                        />
                      )}
                    </HStack>
                  ))}
                  <HStack w="100%" justify="space-between" mt={2}>
                    <Text 
                      fontSize="sm" 
                      color={editFormData.installments.reduce((sum, amount) => sum + (amount || 0), 0) === editFormData.totalAmount ? 'green.600' : 'red.600'}
                      fontWeight="bold"
                    >
                      Installments Total: ₹{editFormData.installments.reduce((sum, amount) => sum + (amount || 0), 0)}
                      {editFormData.installments.reduce((sum, amount) => sum + (amount || 0), 0) === editFormData.totalAmount ? ' ✓' : ' ⚠️'}
                    </Text>
                    <Button
                      leftIcon={<AddIcon />}
                      size="sm"
                      colorScheme="green"
                      onClick={addInstallment}
                    >
                      Add Installment
                    </Button>
                  </HStack>
                </VStack>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button 
              colorScheme="blue" 
              mr={3} 
              onClick={handleUpdatePayment} 
              isLoading={loading}
              isDisabled={editFormData.installments.reduce((sum, amount) => sum + (amount || 0), 0) !== editFormData.totalAmount || editFormData.installments.some(amount => !amount || amount <= 0)}
            >
              Update Payment
            </Button>
            <Button variant="ghost" onClick={onEditClose}>
              Cancel
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Delete Payment
            </AlertDialogHeader>
            <AlertDialogBody>
              Are you sure you want to delete this payment record? This action cannot be undone.
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteClose}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={handleDeletePayment} ml={3} isLoading={loading}>
                Delete
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
}