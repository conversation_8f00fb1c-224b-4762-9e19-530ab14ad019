const express = require('express');
const router = express.Router();

// DELETE /api/payments/transaction/:transactionId
router.delete('/payments/transaction/:transactionId', async (req, res) => {
  try {
    const { transactionId } = req.params;
    
    // Start database transaction
    await db.beginTransaction();
    
    // Delete payment installments first (due to foreign key constraint)
    await db.query('DELETE FROM payment_installments WHERE payment_id = ?', [transactionId]);
    
    // Delete main payment record
    const result = await db.query('DELETE FROM payments WHERE id = ?', [transactionId]);
    
    if (result.affectedRows === 0) {
      await db.rollback();
      return res.status(404).json({
        success: false,
        error: 'Payment transaction not found'
      });
    }
    
    await db.commit();
    
    res.json({
      success: true,
      message: 'Payment transaction deleted successfully'
    });
    
  } catch (error) {
    await db.rollback();
    console.error('Error deleting payment transaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete payment transaction'
    });
  }
});

// Alternative route for compatibility with existing frontend
router.delete('/payments/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    // Start database transaction
    await db.beginTransaction();
    
    // Delete payment installments first
    await db.query('DELETE FROM payment_installments WHERE payment_id = ?', [paymentId]);
    
    // Delete main payment record
    const result = await db.query('DELETE FROM payments WHERE id = ?', [paymentId]);
    
    if (result.affectedRows === 0) {
      await db.rollback();
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }
    
    await db.commit();
    
    res.json({
      success: true,
      message: 'Payment deleted successfully'
    });
    
  } catch (error) {
    await db.rollback();
    console.error('Error deleting payment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete payment'
    });
  }
});

module.exports = router;