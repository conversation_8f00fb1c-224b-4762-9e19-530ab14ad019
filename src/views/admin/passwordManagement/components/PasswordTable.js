import {
  Box,
  Flex,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useColorModeValue,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
  Button,
  HStack,
  VStack,
  IconButton,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  FormControl,
  FormLabel,
  Input,
  Select,
  Badge,
  Checkbox,
  Textarea,
  Stat,
  StatLabel,
  StatNumber,
  SimpleGrid,
} from '@chakra-ui/react';
import Card from 'components/card/Card';
import { FiLock, FiUnlock, FiUsers, FiKey } from 'react-icons/fi';
import React, { useEffect, useState, useCallback, useRef } from 'react';

export default function PasswordTable({ token }) {
  const [data, setData] = useState([]);
  const [stats, setStats] = useState({ total: 0, custom: 0, default: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [classFilter, setClassFilter] = useState('');
  const [sectionFilter, setSectionFilter] = useState('');
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [bulkAction, setBulkAction] = useState('');
  const [bulkPassword, setBulkPassword] = useState('');
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [customPassword, setCustomPassword] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({ current: 1, total: 1, totalRecords: 0 });
  const [searchTerm, setSearchTerm] = useState('');
  const [searchLoading, setSearchLoading] = useState(false);
  const recordsPerPage = 50;
  const searchInputRef = useRef(null);
  const searchTimeoutRef = useRef(null);

  const { isOpen: isBulkOpen, onOpen: onBulkOpen, onClose: onBulkClose } = useDisclosure();
  const { isOpen: isSetOpen, onOpen: onSetOpen, onClose: onSetClose } = useDisclosure();

  const toast = useToast();
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');

  const fetchOverview = useCallback(async (search = '', classF = '', sectionF = '', page = 1, isSearch = false) => {
    try {
      if (isSearch) {
        setSearchLoading(true);
      } else {
        setLoading(true);
      }
      setError(null);
      if (!isSearch) setSelectedStudents([]);
      
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (classF) params.append('class', classF);
      if (sectionF) params.append('section', sectionF);
      params.append('page', page);
      params.append('limit', recordsPerPage);
      
      const url = `${baseUrl}/api/students/password-management/overview?${params}`;
      
      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch password overview: ${response.status}`);
      }

      const result = await response.json();
      const students = Array.isArray(result) ? result : result.students || result.data || [];
      setData(students);
      setStats(result.stats || { total: 0, custom: 0, default: 0 });
      
      if (result.pagination) {
        setPagination(result.pagination);
      } else {
        const totalRecords = result.totalRecords || students.length;
        setPagination({
          current: page,
          total: Math.ceil(totalRecords / recordsPerPage),
          totalRecords: totalRecords
        });
      }
    } catch (err) {
      console.error('Error fetching password overview:', err);
      setError(err.message);
      setData([]);
    } finally {
      setLoading(false);
      setSearchLoading(false);
    }
  }, [token, recordsPerPage]);

  const handleSetPassword = async (studentId, password) => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/${studentId}/set-password`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ new_password: password }),
      });
      
      if (response.ok) {
        toast({ title: 'Success', description: 'Password set successfully', status: 'success', duration: 3000 });
        fetchOverview();
        onSetClose();
      } else {
        throw new Error('Failed to set password');
      }
    } catch (error) {
      toast({ title: 'Error', description: error.message, status: 'error', duration: 3000 });
    }
  };

  const handleRemovePassword = async (studentId) => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/${studentId}/password`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      if (response.ok) {
        toast({ title: 'Success', description: 'Custom password removed', status: 'success', duration: 3000 });
        fetchOverview();
      } else {
        throw new Error('Failed to remove password');
      }
    } catch (error) {
      toast({ title: 'Error', description: error.message, status: 'error', duration: 3000 });
    }
  };

  const handleBulkAction = async () => {
    if (!selectedStudents.length || !bulkAction) return;
    
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const payload = {
        student_ids: selectedStudents,
        action: bulkAction,
      };
      
      if (bulkAction === 'set_custom' && bulkPassword) {
        payload.new_password = bulkPassword;
      }
      
      const response = await fetch(`${baseUrl}/api/students/password-management/bulk-reset`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      if (response.ok) {
        toast({ 
          title: 'Success', 
          description: `Bulk action completed for ${selectedStudents.length} students`, 
          status: 'success', 
          duration: 3000 
        });
        setSelectedStudents([]);
        setBulkAction('');
        setBulkPassword('');
        fetchOverview();
        onBulkClose();
      } else {
        throw new Error('Failed to perform bulk action');
      }
    } catch (error) {
      toast({ title: 'Error', description: error.message, status: 'error', duration: 3000 });
    }
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedStudents(data.map(student => student._id));
    } else {
      setSelectedStudents([]);
    }
  };

  const handleSelectStudent = (studentId, checked) => {
    if (checked) {
      setSelectedStudents([...selectedStudents, studentId]);
    } else {
      setSelectedStudents(selectedStudents.filter(id => id !== studentId));
    }
  };

  const handleSearch = useCallback((value) => {
    setSearchTerm(value);
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    searchTimeoutRef.current = setTimeout(() => {
      setCurrentPage(1);
      fetchOverview(value, classFilter, sectionFilter, 1, true);
    }, 300);
  }, [classFilter, sectionFilter, fetchOverview]);

  const handleFilterChange = useCallback((type, value) => {
    if (type === 'class') {
      setClassFilter(value);
    } else if (type === 'section') {
      setSectionFilter(value);
    }
    setCurrentPage(1);
    fetchOverview(searchTerm, type === 'class' ? value : classFilter, type === 'section' ? value : sectionFilter, 1, true);
  }, [searchTerm, classFilter, sectionFilter, fetchOverview]);

  useEffect(() => {
    if (token) {
      fetchOverview(searchTerm, classFilter, sectionFilter, currentPage, false);
    }
  }, [token]);

  useEffect(() => {
    if (token && currentPage > 1) {
      fetchOverview(searchTerm, classFilter, sectionFilter, currentPage, false);
    }
  }, [currentPage]);

  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  if (loading) {
    return (
      <Card w="100%" px="0px">
        <Flex justifyContent="center" align="center" h="100px">
          <Spinner size="xl" />
        </Flex>
      </Card>
    );
  }

  if (error) {
    return (
      <Card w="100%" px="0px">
        <Alert status="error" borderRadius="md" mb="4">
          <AlertIcon />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </Card>
    );
  }

  return (
    <>
      <Box w="100%" bg="white" p="4">
        {/* Header */}
        <Flex justifyContent="space-between" align="center" mb="4">
          <VStack align="start" spacing="0">
            <Text fontSize="20px" fontWeight="bold" color="gray.800">
              Password Management
            </Text>
            <Text fontSize="xs" color="gray.500">
              🔐 Manage student login passwords
            </Text>
          </VStack>
          <HStack spacing="2">
            {selectedStudents.length > 0 && (
              <Button 
                leftIcon={<FiUsers />} 
                colorScheme="blue" 
                size="sm" 
                onClick={onBulkOpen}
              >
                Bulk Actions ({selectedStudents.length})
              </Button>
            )}
          </HStack>
        </Flex>

        {/* Stats */}
        <SimpleGrid columns={{ base: 1, md: 3 }} spacing="4" mb="4">
          <Card p="4">
            <Stat>
              <StatLabel>Total Students</StatLabel>
              <StatNumber color="blue.500">{stats.total}</StatNumber>
            </Stat>
          </Card>
          <Card p="4">
            <Stat>
              <StatLabel>Custom Passwords</StatLabel>
              <StatNumber color="green.500">{stats.custom}</StatNumber>
            </Stat>
          </Card>
          <Card p="4">
            <Stat>
              <StatLabel>Default Passwords</StatLabel>
              <StatNumber color="orange.500">{stats.default}</StatNumber>
            </Stat>
          </Card>
        </SimpleGrid>

        {/* Search */}
        <Box mb="4" position="relative">
          <Input
            ref={searchInputRef}
            placeholder="Search students by name, class, admission no, or roll no..."
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            size="sm"
            maxW="400px"
          />
          {searchLoading && (
            <Spinner size="sm" position="absolute" right="8px" top="50%" transform="translateY(-50%)" />
          )}
        </Box>

        {/* Filters */}
        <HStack spacing="3" mb="4">
          <Select 
            placeholder="All Classes" 
            size="sm" 
            maxW="150px"
            value={classFilter}
            onChange={(e) => handleFilterChange('class', e.target.value)}
          >
            <option value="10th">10th</option>
            <option value="11th">11th</option>
            <option value="12th">12th</option>
          </Select>
          <Select 
            placeholder="All Sections" 
            size="sm" 
            maxW="150px"
            value={sectionFilter}
            onChange={(e) => handleFilterChange('section', e.target.value)}
          >
            <option value="A">A</option>
            <option value="B">B</option>
            <option value="C">C</option>
          </Select>
        </HStack>

        {/* Table */}
        <Box overflowX="auto" overflowY="auto" maxH="60vh" position="relative">
          {(loading || searchLoading) && (
            <Flex position="absolute" top="50%" left="50%" transform="translate(-50%, -50%)" zIndex="10">
              <Spinner size="lg" />
            </Flex>
          )}
          <Table variant="simple" size="sm" opacity={(loading || searchLoading) ? 0.5 : 1}>
            <Thead bg="gray.50" position="sticky" top="0" zIndex="10">
              <Tr>
                <Th w="40px" textAlign="center">
                  <Checkbox 
                    isChecked={selectedStudents.length === data.length && data.length > 0}
                    isIndeterminate={selectedStudents.length > 0 && selectedStudents.length < data.length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                </Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600">STUDENT</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600">CLASS</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600">PASSWORD STATUS</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600">CURRENT PASSWORD</Th>
                <Th fontSize="10px" color="gray.500" fontWeight="600">ACTIONS</Th>
              </Tr>
            </Thead>
            <Tbody>
              {data.map((student) => (
                <Tr key={student._id} _hover={{ bg: "gray.50" }}>
                  <Td textAlign="center">
                    <Checkbox 
                      isChecked={selectedStudents.includes(student._id)}
                      onChange={(e) => handleSelectStudent(student._id, e.target.checked)}
                    />
                  </Td>
                  <Td>
                    <VStack align="start" spacing="0">
                      <Text fontWeight="medium" fontSize="sm" color="gray.800">
                        {student.name}
                      </Text>
                      <Text fontSize="xs" color="gray.500">
                        ID: {student.no || (student._id ? student._id.slice(-6) : 'N/A')}
                      </Text>
                    </VStack>
                  </Td>
                  <Td>
                    <Badge colorScheme="blue" variant="subtle">
                      {student.class} {student.section}
                    </Badge>
                  </Td>
                  <Td>
                    {student.hasCustomPassword ? (
                      <Badge colorScheme="green" leftIcon={<FiLock />}>
                        Custom
                      </Badge>
                    ) : (
                      <Badge colorScheme="orange" leftIcon={<FiUnlock />}>
                        Default
                      </Badge>
                    )}
                  </Td>
                  <Td>
                    <Text fontSize="sm" fontFamily="mono" color="gray.600">
                      {student.currentPassword || '••••••••'}
                    </Text>
                  </Td>
                  <Td>
                    <HStack spacing="1">
                      <IconButton 
                        size="xs" 
                        variant="ghost" 
                        icon={<FiKey />} 
                        colorScheme="blue"
                        onClick={() => {
                          setSelectedStudent(student);
                          setCustomPassword('');
                          onSetOpen();
                        }}
                        title="Set Custom Password"
                      />
                      {student.hasCustomPassword && (
                        <IconButton 
                          size="xs" 
                          variant="ghost" 
                          icon={<FiUnlock />} 
                          colorScheme="orange"
                          onClick={() => handleRemovePassword(student._id)}
                          title="Remove Custom Password"
                        />
                      )}
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>

        {data.length === 0 && (
          <Flex justifyContent="center" align="center" h="100px">
            <Text color="gray.500">No students found</Text>
          </Flex>
        )}

        {/* Pagination */}
        {data.length > 0 && (
          <Flex justifyContent="space-between" alignItems="center" mt="3" pt="3" borderTop="1px" borderColor="gray.100">
            <Text fontSize="xs" color="gray.500">
              {((currentPage - 1) * recordsPerPage) + 1} to {Math.min(((currentPage - 1) * recordsPerPage) + data.length, pagination.totalRecords || data.length)} of {pagination.totalRecords || data.length}
            </Text>
            <HStack spacing="1">
              <IconButton 
                size="xs" 
                variant="ghost" 
                icon={<Text>←</Text>} 
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))} 
                isDisabled={currentPage === 1} 
              />
              {Array.from({ length: Math.min(5, Math.ceil((pagination.totalRecords || data.length) / recordsPerPage)) }, (_, i) => i + 1).map(page => (
                <Button 
                  key={page}
                  size="xs" 
                  colorScheme={currentPage === page ? "blue" : "gray"} 
                  variant={currentPage === page ? "solid" : "ghost"} 
                  minW="6"
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              ))}
              <IconButton 
                size="xs" 
                variant="ghost" 
                icon={<Text>→</Text>} 
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil((pagination.totalRecords || data.length) / recordsPerPage)))} 
                isDisabled={currentPage >= Math.ceil((pagination.totalRecords || data.length) / recordsPerPage)} 
              />
            </HStack>
          </Flex>
        )}
      </Box>

      {/* Set Password Modal */}
      <Modal isOpen={isSetOpen} onClose={onSetClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Set Custom Password</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedStudent && (
              <VStack spacing="4" align="stretch">
                <Box>
                  <Text fontWeight="bold">Student:</Text>
                  <Text>{selectedStudent.name} ({selectedStudent.class} {selectedStudent.section})</Text>
                </Box>
                <FormControl>
                  <FormLabel>New Password</FormLabel>
                  <Input 
                    type="password"
                    value={customPassword}
                    onChange={(e) => setCustomPassword(e.target.value)}
                    placeholder="Enter new password"
                  />
                </FormControl>
                <Box p="3" bg="gray.50" borderRadius="md">
                  <Text fontSize="sm" color="gray.600">
                    <strong>Password Priority:</strong><br/>
                    1. Custom password (if set)<br/>
                    2. Birthday (ddmmyyyy format)<br/>
                    3. Default (hariom8989)
                  </Text>
                </Box>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onSetClose}>Cancel</Button>
            <Button 
              colorScheme="blue" 
              onClick={() => handleSetPassword(selectedStudent._id, customPassword)}
              isDisabled={!customPassword}
            >
              Set Password
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Bulk Actions Modal */}
      <Modal isOpen={isBulkOpen} onClose={onBulkClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Bulk Password Actions</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing="4" align="stretch">
              <Box>
                <Text fontWeight="bold">Selected Students: {selectedStudents.length}</Text>
              </Box>
              <FormControl>
                <FormLabel>Action</FormLabel>
                <Select 
                  value={bulkAction}
                  onChange={(e) => setBulkAction(e.target.value)}
                  placeholder="Select action"
                >
                  <option value="set_custom">Set Custom Password</option>
                  <option value="remove_custom">Remove Custom Passwords</option>
                </Select>
              </FormControl>
              {bulkAction === 'set_custom' && (
                <FormControl>
                  <FormLabel>New Password</FormLabel>
                  <Input 
                    type="password"
                    value={bulkPassword}
                    onChange={(e) => setBulkPassword(e.target.value)}
                    placeholder="Enter password for all selected students"
                  />
                </FormControl>
              )}
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onBulkClose}>Cancel</Button>
            <Button 
              colorScheme="blue" 
              onClick={handleBulkAction}
              isDisabled={!bulkAction || (bulkAction === 'set_custom' && !bulkPassword)}
            >
              Apply Action
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}