import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Badge,
  Text,
  HStack,
  VStack,
  useToast,
  IconButton,
  Spinner
} from '@chakra-ui/react';
import { FiDownload, FiTrash2, FiRefreshCw } from 'react-icons/fi';
import { useAuth } from 'contexts/AuthContext';

export default function ExportHistory() {
  const { token } = useAuth();
  const toast = useToast();
  const [exports, setExports] = useState([]);
  const [loading, setLoading] = useState(true);

  const fetchHistory = async () => {
    try {
      setLoading(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      console.log('Fetching export history from:', `${baseUrl}/api/export/history`);
      
      const response = await fetch(`${baseUrl}/api/export/history`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('Export history response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Export history data:', data);
        setExports(data.exports || data || []);
      } else if (response.status === 404) {
        // Fallback: Try students export history endpoint
        console.log('Trying fallback endpoint...');
        const fallbackResponse = await fetch(`${baseUrl}/api/students/export/history`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();
          console.log('Fallback data:', fallbackData);
          setExports(fallbackData.exports || fallbackData || []);
        } else {
          setExports([]);
          toast({
            title: 'Export history not available',
            description: 'API endpoint not implemented yet',
            status: 'warning',
            duration: 3000
          });
        }
      } else {
        const errorText = await response.text();
        console.error('Export history error:', errorText);
        toast({
          title: 'Failed to load export history',
          description: `Status: ${response.status}`,
          status: 'error',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('Failed to fetch export history:', error);
      toast({
        title: 'Network Error',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const downloadFile = async (filename) => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/export/download/${filename}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      toast({
        title: 'Download Failed',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    }
  };

  const deleteFile = async (filename) => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/export/${filename}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        toast({
          title: 'File Deleted',
          description: 'Export file deleted successfully',
          status: 'success',
          duration: 3000
        });
        fetchHistory();
      }
    } catch (error) {
      toast({
        title: 'Delete Failed',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    }
  };

  const cleanup = async () => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/export/cleanup`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: 'Cleanup Complete',
          description: `${data.deletedCount} files cleaned up`,
          status: 'success',
          duration: 3000
        });
        fetchHistory();
      }
    } catch (error) {
      toast({
        title: 'Cleanup Failed',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    }
  };

  useEffect(() => {
    fetchHistory();
  }, []);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <VStack spacing="4" py="8">
        <Spinner size="lg" />
        <Text>Loading export history...</Text>
      </VStack>
    );
  }

  return (
    <Box>
      <HStack justify="space-between" mb="4">
        <Text fontSize="lg" fontWeight="bold">Export History</Text>
        <HStack>
          <Button size="sm" leftIcon={<FiRefreshCw />} onClick={fetchHistory}>
            Refresh
          </Button>
          <Button size="sm" colorScheme="red" onClick={cleanup}>
            Cleanup Old Files
          </Button>
        </HStack>
      </HStack>

      <Box overflowX="auto">
        <Table variant="simple" size="sm">
          <Thead>
            <Tr>
              <Th>Filename</Th>
              <Th>Records</Th>
              <Th>Size</Th>
              <Th>Created</Th>
              <Th>Downloads</Th>
              <Th>Actions</Th>
            </Tr>
          </Thead>
          <Tbody>
            {exports.map((exp) => (
              <Tr key={exp.filename}>
                <Td>
                  <Text fontSize="sm" fontFamily="mono">
                    {exp.filename}
                  </Text>
                </Td>
                <Td>
                  <Badge colorScheme="blue">{exp.recordCount}</Badge>
                </Td>
                <Td>{formatFileSize(exp.fileSize)}</Td>
                <Td>{formatDate(exp.createdAt)}</Td>
                <Td>
                  <Badge colorScheme="green">{exp.downloadCount || 0}</Badge>
                </Td>
                <Td>
                  <HStack spacing="1">
                    <IconButton
                      size="xs"
                      icon={<FiDownload />}
                      onClick={() => downloadFile(exp.filename)}
                      title="Download"
                    />
                    <IconButton
                      size="xs"
                      icon={<FiTrash2 />}
                      colorScheme="red"
                      onClick={() => deleteFile(exp.filename)}
                      title="Delete"
                    />
                  </HStack>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>

      {exports.length === 0 && (
        <Text textAlign="center" py="8" color="gray.500">
          No export history found
        </Text>
      )}
    </Box>
  );
}