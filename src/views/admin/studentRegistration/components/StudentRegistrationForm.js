/* eslint-disable */

import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Text,
  Textarea,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertDescription,
  SimpleGrid,
  Spinner,
  VStack,
  useToast,
  Select,
} from '@chakra-ui/react';
// Custom components
import Card from 'components/card/Card';
import React, { useState } from 'react';
import { useAuth } from 'contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import PhotoManager from 'components/photoUpload/PhotoManager';

export default function StudentRegistrationForm() {
  const { token } = useAuth();
  const navigate = useNavigate();
  const toast = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [createdStudentId, setCreatedStudentId] = useState(null);
  
  // Form state
  const [formData, setFormData] = useState({
    no: '',
    roll_number: '',
    class: '',
    name: '',
    address: '',
    mobile_number: '',
    dob: '',
    section: '',
    school_id: 1,
    admission_no: '',
    admission_date: '',
    academic_year: '2024-25',
    status: 'Active',
    gender: '',
    blood_group: '',
    father_name: '',
    father_phone: '',
    mother_name: '',
    mother_phone: '',
    contact_number: '',
    email: '',
    emergency_contact: '',
  });
  


  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const inputBg = useColorModeValue('secondaryGray.300', 'navy.900');
  const inputText = useColorModeValue('secondaryGray.900', 'white');
  const borderColor = useColorModeValue('secondaryGray.100', 'whiteAlpha.100');

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };
  


  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {

      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/register`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to register student');
      }

      const result = await response.json();
      setSuccess(true);
      setCreatedStudentId(result._id);
      
      // Show success message with student info
      toast({
        title: 'Student Registered Successfully!',
        description: `${result.name} has been registered. You can now upload photos.`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      
      // Reset form after 3 seconds to allow user to see success message
      setTimeout(() => {
        setFormData({
          no: '',
          roll_number: '',
          class: '',
          name: '',
          address: '',
          mobile_number: '',
          dob: '',
          section: '',
          school_id: 1,
          admission_no: '',
          admission_date: '',
          academic_year: '2024-25',
          status: 'Active',
          gender: '',
          blood_group: '',
          father_name: '',
          father_phone: '',
          mother_name: '',
          mother_phone: '',
          contact_number: '',
          email: '',
          emergency_contact: '',
        });
        setSuccess(false);
        setCreatedStudentId(null);
      }, 3000);

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card flexDirection="column" w="100%" px="0px">
      <Flex px="25px" mb="8px" justifyContent="space-between" align="center">
        <Heading color={textColor} fontSize="22px" fontWeight="700" lineHeight="100%">
          Student Registration
        </Heading>
      </Flex>
      
      <Box px="25px">
        {error && (
          <Alert status="error" borderRadius="md" mb="4">
            <AlertIcon />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && createdStudentId && (
          <Alert status="success" borderRadius="md" mb="4">
            <AlertIcon />
            <AlertDescription>
              Student registered successfully! Upload photos below.
            </AlertDescription>
          </Alert>
        )}
        
        {success && createdStudentId && (
          <PhotoManager
            studentId={createdStudentId}
            studentData={{
              name: formData.name,
              father_name: formData.father_name,
              mother_name: formData.mother_name
            }}
            onPhotoUpdate={() => {}}
          />
        )}
        
        <form onSubmit={handleSubmit}>
          <SimpleGrid columns={{ base: 1, md: 3 }} gap="20px" mb="20px">
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Student No.* (e.g., 100)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Student No"
                name="no"
                value={formData.no}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Roll Number* (e.g., 100)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Roll Number"
                name="roll_number"
                value={formData.roll_number}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Class* (e.g., 10th)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Class"
                name="class"
                value={formData.class}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Name* (e.g., Dhimahi Patel)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Section* (e.g., A)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Section"
                name="section"
                value={formData.section}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Admission No.* (e.g., SS100)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Admission No"
                name="admission_no"
                value={formData.admission_no}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Mobile Number* (e.g., 9033595789)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Mobile Number"
                name="mobile_number"
                value={formData.mobile_number}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Date of Birth*
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                type="date"
                placeholder="Date of Birth"
                name="dob"
                value={formData.dob}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Gender* (e.g., Male/Female)
              </FormLabel>
              <Select
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Select Gender"
                name="gender"
                value={formData.gender}
                onChange={handleChange}
              >
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </Select>
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Blood Group (e.g., A+, B+)
              </FormLabel>
              <Select
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Select Blood Group"
                name="blood_group"
                value={formData.blood_group}
                onChange={handleChange}
              >
                <option value="A+">A+</option>
                <option value="A-">A-</option>
                <option value="B+">B+</option>
                <option value="B-">B-</option>
                <option value="AB+">AB+</option>
                <option value="AB-">AB-</option>
                <option value="O+">O+</option>
                <option value="O-">O-</option>
              </Select>
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Admission Date*
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                type="date"
                name="admission_date"
                value={formData.admission_date}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Father Name* (e.g., Hemant Patel)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Father Name"
                name="father_name"
                value={formData.father_name}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Father Phone* (e.g., 9033595789)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Father Phone"
                name="father_phone"
                value={formData.father_phone}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Mother Name* (e.g., Artiben Patel)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Mother Name"
                name="mother_name"
                value={formData.mother_name}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Mother Phone* (e.g., 9033595789)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Mother Phone"
                name="mother_phone"
                value={formData.mother_phone}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Contact Number* (e.g., 9033595789)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Contact Number"
                name="contact_number"
                value={formData.contact_number}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Email (e.g., <EMAIL>)
              </FormLabel>
              <Input
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Email"
                name="email"
                value={formData.email}
                onChange={handleChange}
              />
            </FormControl>
            
            <FormControl>
              <FormLabel
                display="flex"
                ms="4px"
                fontSize="sm"
                fontWeight="500"
                color={textColor}
                mb="8px"
              >
                Emergency Contact* (e.g., 9033595789)
              </FormLabel>
              <Input
                isRequired={true}
                variant="auth"
                fontSize="sm"
                bg={inputBg}
                color={inputText}
                fontWeight="500"
                borderColor={borderColor}
                borderRadius="16px"
                placeholder="Emergency Contact"
                name="emergency_contact"
                value={formData.emergency_contact}
                onChange={handleChange}
              />
            </FormControl>
          </SimpleGrid>
          
          <FormControl mb="20px">
            <FormLabel
              display="flex"
              ms="4px"
              fontSize="sm"
              fontWeight="500"
              color={textColor}
              mb="8px"
            >
              Address* (e.g., Valsad, Gujarat)
            </FormLabel>
            <Textarea
              isRequired={true}
              variant="auth"
              fontSize="sm"
              bg={inputBg}
              color={inputText}
              fontWeight="500"
              borderColor={borderColor}
              borderRadius="16px"
              placeholder="Address"
              name="address"
              value={formData.address}
              onChange={handleChange}
              rows={3}
            />
          </FormControl>
          
          <Button
            variant="brand"
            fontSize="sm"
            fontWeight="500"
            w="100%"
            h="50"
            mb="24px"
            type="submit"
            isLoading={loading}
            loadingText="Registering Student"
          >
            Register Student
          </Button>
        </form>
      </Box>
    </Card>
  );
}
