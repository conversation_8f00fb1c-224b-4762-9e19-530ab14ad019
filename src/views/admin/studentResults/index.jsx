import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  VStack,
  HStack,
  Badge,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Spinner,
  Select,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  Grid,
  GridItem,
  Icon,
  Avatar,
  Textarea,
  NumberInput,
  NumberInputField,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
} from '@chakra-ui/react';
import { FiUsers, FiTrendingUp, FiDownload, FiMail, FiMessageSquare, FiPlus } from 'react-icons/fi';
import Card from 'components/card/Card';
import { useAuth } from 'contexts/AuthContext';

export default function StudentResults() {
  const { token } = useAuth();
  const [students, setStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterClass, setFilterClass] = useState('');
  const [newResult, setNewResult] = useState({
    examName: '',
    subject: '',
    maxMarks: '',
    obtainedMarks: '',
    examDate: '',
    remarks: ''
  });

  const { isOpen: isAddResultOpen, onOpen: onAddResultOpen, onClose: onAddResultClose } = useDisclosure();
  const { isOpen: isProgressOpen, onOpen: onProgressOpen, onClose: onProgressClose } = useDisclosure();
  const toast = useToast();

  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');

  useEffect(() => {
    fetchStudents();
  }, []);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      const data = await response.json();
      setStudents(Array.isArray(data) ? data : data.students || []);
    } catch (error) {
      console.error('Error fetching students:', error);
      setStudents([]);
    } finally {
      setLoading(false);
    }
  };

  const handleExportPDF = async (student) => {
    toast({ title: 'Success', description: `PDF exported for ${student.name}`, status: 'success', duration: 3000 });
  };

  const handleSendMessage = async (type, student) => {
    toast({ 
      title: 'Success', 
      description: `Results sent via ${type === 'email' ? 'email' : 'WhatsApp'} to ${student.name}`, 
      status: 'success', 
      duration: 3000 
    });
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.admission_number?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = !filterClass || student.class === filterClass;
    return matchesSearch && matchesClass;
  });

  if (loading) {
    return (
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
        <Flex justifyContent="center" align="center" h="200px">
          <Spinner size="xl" />
        </Flex>
      </Box>
    );
  }

  return (
    <>
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
        <VStack spacing="6" align="stretch">
          <Flex justify="space-between" align="center">
            <Text fontSize="2xl" fontWeight="bold" color={textColor}>
              Student Results Management
            </Text>
            <Button leftIcon={<Icon as={FiPlus} />} colorScheme="blue" onClick={onAddResultOpen}>
              Add Result
            </Button>
          </Flex>

          <Grid templateColumns="repeat(3, 1fr)" gap="6">
            <GridItem>
              <Card p="6">
                <Stat>
                  <HStack>
                    <Box p="3" bg="blue.100" borderRadius="lg">
                      <Icon as={FiUsers} color="blue.500" boxSize="6" />
                    </Box>
                    <VStack align="start" spacing="0">
                      <StatNumber fontSize="2xl" color={textColor}>
                        {students.length}
                      </StatNumber>
                      <StatLabel color="gray.500">Total Students</StatLabel>
                    </VStack>
                  </HStack>
                </Stat>
              </Card>
            </GridItem>
            
            <GridItem>
              <Card p="6">
                <Stat>
                  <HStack>
                    <Box p="3" bg="green.100" borderRadius="lg">
                      <Icon as={FiTrendingUp} color="green.500" boxSize="6" />
                    </Box>
                    <VStack align="start" spacing="0">
                      <StatNumber fontSize="2xl" color={textColor}>
                        85.2%
                      </StatNumber>
                      <StatLabel color="gray.500">Average Performance</StatLabel>
                    </VStack>
                  </HStack>
                </Stat>
              </Card>
            </GridItem>
            
            <GridItem>
              <Card p="6">
                <Stat>
                  <HStack>
                    <Box p="3" bg="purple.100" borderRadius="lg">
                      <Icon as={FiDownload} color="purple.500" boxSize="6" />
                    </Box>
                    <VStack align="start" spacing="0">
                      <StatNumber fontSize="2xl" color={textColor}>
                        24
                      </StatNumber>
                      <StatLabel color="gray.500">Reports Generated</StatLabel>
                    </VStack>
                  </HStack>
                </Stat>
              </Card>
            </GridItem>
          </Grid>

          <Card p="6">
            <HStack spacing="4">
              <FormControl maxW="300px">
                <Input
                  placeholder="Search students..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </FormControl>
              
              <FormControl maxW="150px">
                <Select
                  placeholder="All Classes"
                  value={filterClass}
                  onChange={(e) => setFilterClass(e.target.value)}
                >
                  <option value="10th">10th</option>
                  <option value="11th">11th</option>
                  <option value="12th">12th</option>
                </Select>
              </FormControl>
            </HStack>
          </Card>

          <Card p="0" overflow="hidden">
            <Box p="6" bg="gray.50" borderBottom="1px" borderColor={borderColor}>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                Students Overview
              </Text>
            </Box>
            
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Student</Th>
                  <Th>Class</Th>
                  <Th>Overall Progress</Th>
                  <Th>Last Result</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredStudents.map((student) => (
                  <Tr key={student._id}>
                    <Td>
                      <HStack>
                        <Avatar size="sm" name={student.name} />
                        <VStack align="start" spacing="0">
                          <Text fontWeight="semibold">{student.name}</Text>
                          <Text fontSize="sm" color="gray.500">
                            {student.admission_number}
                          </Text>
                        </VStack>
                      </HStack>
                    </Td>
                    <Td>
                      <Badge colorScheme="blue" variant="subtle">
                        {student.class}
                      </Badge>
                    </Td>
                    <Td>
                      <VStack align="start" spacing="1">
                        <Progress value={75} colorScheme="green" size="sm" w="100px" />
                        <Text fontSize="sm" color="gray.600">75%</Text>
                      </VStack>
                    </Td>
                    <Td>
                      <Badge colorScheme="green" variant="solid">A</Badge>
                    </Td>
                    <Td>
                      <HStack spacing="2">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => {
                            setSelectedStudent(student);
                            onProgressOpen();
                          }}
                        >
                          View Progress
                        </Button>
                        <Button 
                          size="sm" 
                          colorScheme="blue" 
                          variant="outline"
                          leftIcon={<Icon as={FiDownload} />}
                          onClick={() => handleExportPDF(student)}
                        >
                          PDF
                        </Button>
                        <Button 
                          size="sm" 
                          colorScheme="green" 
                          variant="outline"
                          leftIcon={<Icon as={FiMail} />}
                          onClick={() => handleSendMessage('email', student)}
                        >
                          Email
                        </Button>
                        <Button 
                          size="sm" 
                          colorScheme="whatsapp" 
                          variant="outline"
                          leftIcon={<Icon as={FiMessageSquare} />}
                          onClick={() => handleSendMessage('whatsapp', student)}
                        >
                          WhatsApp
                        </Button>
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Card>
        </VStack>
      </Box>

      <Modal isOpen={isAddResultOpen} onClose={onAddResultClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Add New Result</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing="4" align="stretch">
              <FormControl>
                <FormLabel>Select Student</FormLabel>
                <Select placeholder="Choose student">
                  {students.map(student => (
                    <option key={student._id} value={student._id}>
                      {student.name} - {student.class}
                    </option>
                  ))}
                </Select>
              </FormControl>

              <HStack spacing="4">
                <FormControl>
                  <FormLabel>Exam Name</FormLabel>
                  <Input placeholder="Mid-term, Final, etc." />
                </FormControl>
                <FormControl>
                  <FormLabel>Subject</FormLabel>
                  <Input placeholder="Mathematics, Science, etc." />
                </FormControl>
              </HStack>

              <HStack spacing="4">
                <FormControl>
                  <FormLabel>Max Marks</FormLabel>
                  <NumberInput>
                    <NumberInputField placeholder="100" />
                  </NumberInput>
                </FormControl>
                <FormControl>
                  <FormLabel>Obtained Marks</FormLabel>
                  <NumberInput>
                    <NumberInputField placeholder="85" />
                  </NumberInput>
                </FormControl>
              </HStack>

              <FormControl>
                <FormLabel>Exam Date</FormLabel>
                <Input type="date" />
              </FormControl>

              <FormControl>
                <FormLabel>Remarks</FormLabel>
                <Textarea placeholder="Additional comments..." />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onAddResultClose}>
              Cancel
            </Button>
            <Button colorScheme="blue">
              Add Result
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <Modal isOpen={isProgressOpen} onClose={onProgressClose} size="4xl">
        <ModalOverlay />
        <ModalContent maxH="90vh">
          <ModalHeader>
            Student Progress - {selectedStudent?.name}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody overflowY="auto">
            {selectedStudent && (
              <Tabs>
                <TabList>
                  <Tab>Overview</Tab>
                  <Tab>Detailed Results</Tab>
                  <Tab>Progress Chart</Tab>
                </TabList>

                <TabPanels>
                  <TabPanel>
                    <VStack spacing="6" align="stretch">
                      <Grid templateColumns="repeat(3, 1fr)" gap="4">
                        <Card p="4">
                          <Stat>
                            <StatLabel>Overall Average</StatLabel>
                            <StatNumber color="green.500">78.5%</StatNumber>
                          </Stat>
                        </Card>
                        <Card p="4">
                          <Stat>
                            <StatLabel>Total Exams</StatLabel>
                            <StatNumber>12</StatNumber>
                          </Stat>
                        </Card>
                        <Card p="4">
                          <Stat>
                            <StatLabel>Current Grade</StatLabel>
                            <StatNumber color="green.500">B+</StatNumber>
                          </Stat>
                        </Card>
                      </Grid>

                      <Card p="4">
                        <Text fontWeight="bold" mb="4">Subject-wise Performance</Text>
                        <VStack spacing="3" align="stretch">
                          {['Mathematics', 'Science', 'English', 'History'].map(subject => (
                            <HStack key={subject} justify="space-between">
                              <Text>{subject}</Text>
                              <HStack>
                                <Progress value={Math.random() * 100} colorScheme="blue" size="sm" w="100px" />
                                <Text fontSize="sm" w="40px">{Math.floor(Math.random() * 100)}%</Text>
                              </HStack>
                            </HStack>
                          ))}
                        </VStack>
                      </Card>
                    </VStack>
                  </TabPanel>

                  <TabPanel>
                    <Table variant="simple" size="sm">
                      <Thead>
                        <Tr>
                          <Th>Exam</Th>
                          <Th>Subject</Th>
                          <Th>Marks</Th>
                          <Th>Percentage</Th>
                          <Th>Grade</Th>
                          <Th>Date</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        <Tr>
                          <Td>Mid-term</Td>
                          <Td>Mathematics</Td>
                          <Td>85/100</Td>
                          <Td>85%</Td>
                          <Td><Badge colorScheme="green">A</Badge></Td>
                          <Td>2025-01-15</Td>
                        </Tr>
                        <Tr>
                          <Td>Final</Td>
                          <Td>Science</Td>
                          <Td>78/100</Td>
                          <Td>78%</Td>
                          <Td><Badge colorScheme="blue">B+</Badge></Td>
                          <Td>2025-01-20</Td>
                        </Tr>
                      </Tbody>
                    </Table>
                  </TabPanel>

                  <TabPanel>
                    <Box h="300px" bg="gray.50" borderRadius="md" p="4">
                      <Text textAlign="center" color="gray.500" mt="20">
                        Progress Chart Visualization
                        <br />
                        (Chart component would be integrated here)
                      </Text>
                    </Box>
                  </TabPanel>
                </TabPanels>
              </Tabs>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onProgressClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}