import React, { useState } from 'react';
import {
  Box,
  Button,
  Image,
  Input,
  VStack,
  Text,
  useToast,
  Progress,
  Avatar,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure
} from '@chakra-ui/react';
import { FiCamera, FiUpload } from 'react-icons/fi';
import { useAuth } from 'contexts/AuthContext';

export default function PhotoUpload({ studentId, currentPhoto, onPhotoUpdate, showUpload = true, studentName = "Student" }) {
  const { token } = useAuth();
  
  const getInitials = (name) => {
    const names = name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  const getImageUrl = (photoPath) => {
    if (!photoPath) return null;
    if (photoPath.startsWith('http')) return photoPath;
    
    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    
    // Extract filename from path if it's a full path
    const filename = photoPath.includes('/') ? photoPath.split('/').pop() : photoPath;
    
    // Use the correct API route with /photo/ part
    return `${baseUrl}/api/students/photo/${filename}`;
  };
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const validateFile = (file) => {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    
    if (file.size > maxSize) {
      throw new Error('File size must be less than 5MB');
    }
    
    if (!allowedTypes.includes(file.type.toLowerCase())) {
      throw new Error('Only JPG, JPEG, PNG, GIF, and WEBP files are allowed');
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      validateFile(file);
      setSelectedFile(file);
      setPreview(URL.createObjectURL(file));
    } catch (error) {
      toast({
        title: 'Invalid File',
        description: error.message,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const uploadPhoto = async () => {
    if (!selectedFile) return;

    const formData = new FormData();
    formData.append('photo', selectedFile);

    try {
      setUploading(true);
      setProgress(20);

      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/${studentId}/upload-photo`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      setProgress(80);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || `HTTP ${response.status}`);
      }

      const data = await response.json();
      setProgress(100);

      if (data.success) {
        toast({
          title: 'Success',
          description: data.message || 'Profile picture uploaded successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        
        if (onPhotoUpdate) {
          onPhotoUpdate();
        }
        
        onClose();
        setSelectedFile(null);
        setPreview(null);
      } else {
        throw new Error(data.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Photo upload error:', error);
      toast({
        title: 'Upload Failed',
        description: error.message || 'Failed to upload photo',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  return (
    <>
      <Box position="relative" display="inline-block">
        <Avatar
          size="sm"
          src={getImageUrl(currentPhoto)}
          name={getInitials(studentName)}
          bg="blue.500"
          color="white"
        />
        {showUpload && (
          <IconButton
            icon={<FiCamera />}
            size="xs"
            colorScheme="blue"
            borderRadius="full"
            position="absolute"
            bottom="-2px"
            right="-2px"
            onClick={onOpen}
          />
        )}
      </Box>

      <Modal isOpen={isOpen} onClose={onClose} size="md">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Upload Profile Picture</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb="6">
            <VStack spacing="4">
              {preview && (
                <Image
                  src={preview}
                  alt="Preview"
                  boxSize="200px"
                  objectFit="cover"
                  borderRadius="lg"
                />
              )}
              
              <Input
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                onChange={handleFileSelect}
                display="none"
                id="photo-upload"
              />
              
              <Button
                as="label"
                htmlFor="photo-upload"
                leftIcon={<FiUpload />}
                colorScheme="gray"
                variant="outline"
                cursor="pointer"
              >
                Choose Photo
              </Button>

              {uploading && (
                <Box w="full">
                  <Text fontSize="sm" mb="2">Uploading...</Text>
                  <Progress value={progress} colorScheme="blue" />
                </Box>
              )}

              <Button
                colorScheme="blue"
                onClick={uploadPhoto}
                isDisabled={!selectedFile || uploading}
                isLoading={uploading}
                w="full"
              >
                Upload Photo
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}