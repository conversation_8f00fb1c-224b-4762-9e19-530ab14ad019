# Payment API Implementation Prompt

## Database Schema Required:

```sql
-- Main payments table
CREATE TABLE payments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  student_id INT NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL,
  payment_type ENUM('tuition', 'exam', 'transport', 'library', 'other') NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES students(id)
);

-- Installments table
CREATE TABLE payment_installments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  payment_id INT NOT NULL,
  installment_number INT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  paid_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
);
```

## API Endpoint Required:

**POST /api/payments**

### Request Body Format:
```json
{
  "studentId": 123,
  "totalAmount": 50000,
  "installments": [25000, 10000, 15000],
  "paymentType": "tuition",
  "description": "Annual tuition fee payment"
}
```

### Expected Response:
```json
{
  "success": true,
  "paymentId": 456,
  "message": "Payment recorded successfully"
}
```

### Implementation Requirements:
1. Use database transaction to ensure data integrity
2. Insert main payment record first
3. Insert each installment with sequential installment_number (1, 2, 3...)
4. Rollback transaction if any step fails
5. Return payment ID on success
6. Handle errors with proper HTTP status codes

### Error Handling:
- Return 400 for validation errors
- Return 500 for database errors
- Include error message in response

## DELETE Payment Transaction API:

**DELETE /api/payments/transaction/:transactionId**

### Expected Response:
```json
{
  "success": true,
  "message": "Payment transaction deleted successfully"
}
```

### Implementation Requirements:
1. Use database transaction to ensure data integrity
2. Delete payment installments first (due to foreign key constraint)
3. Delete main payment record
4. Rollback transaction if any step fails
5. Return 404 if payment not found
6. Return success message on completion

### Error Handling:
- Return 404 if payment transaction not found
- Return 500 for database errors
- Include error message in response