import { Box, SimpleGrid } from "@chakra-ui/react";
import PasswordTable from "views/admin/passwordManagement/components/PasswordTable";
import React from "react";
import { useAuth } from "contexts/AuthContext";

export default function PasswordManagement() {
  const { token } = useAuth();
  
  return (
    <Box pt={{ base: "130px", md: "80px", xl: "80px" }}>
      <SimpleGrid
        mb='20px'
        columns={{ sm: 1, md: 1 }}
        spacing={{ base: "20px", xl: "20px" }}>
        <PasswordTable token={token} />
      </SimpleGrid>
    </Box>
  );
}