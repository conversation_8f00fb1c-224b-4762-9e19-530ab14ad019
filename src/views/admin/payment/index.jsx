import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Select,
  Text,
  useColorModeValue,
  VStack,
  HStack,
  Alert,
  AlertIcon,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import Card from 'components/card/Card';
import { useAuth } from 'contexts/AuthContext';

export default function Payment() {
  const { token } = useAuth();
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [paymentData, setPaymentData] = useState({
    amount: '',
    paymentType: '',
    description: '',
  });
  const [totalAmount, setTotalAmount] = useState(0);
  const [installments, setInstallments] = useState([]);
  const [currentInstallment, setCurrentInstallment] = useState('');
  const [currentEmiDate, setCurrentEmiDate] = useState('');
  const [emiDates, setEmiDates] = useState([]);
  const [paidAmount, setPaidAmount] = useState(0);
  const [alertMessage, setAlertMessage] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const cancelRef = React.useRef();
  const searchRef = React.useRef();
  const toast = useToast();

  const textColor = useColorModeValue('secondaryGray.900', 'white');

  useEffect(() => {
    fetchStudents();
  }, []);

  useEffect(() => {
    if (searchTerm && !selectedStudent) {
      const filtered = students.filter(student =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.roll_number.toString().includes(searchTerm) ||
        student.class.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredStudents(filtered);
    } else {
      setFilteredStudents([]);
    }
  }, [searchTerm, students, selectedStudent]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setFilteredStudents([]);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      const studentsData = Array.isArray(data) ? data : data.students || data.data || [];
      setStudents(studentsData);
    } catch (error) {
      console.error('Error fetching students:', error);
      setStudents([]);
    } finally {
      setLoading(false);
    }
  };

  const handleStudentSelect = (student) => {
    setSelectedStudent(student);
    setSearchTerm(student.name);
    setFilteredStudents([]);
  };

  const handleTotalAmountSet = () => {
    const amount = parseFloat(paymentData.amount);
    if (amount > 0) {
      setTotalAmount(amount);
      setInstallments([]);
      setEmiDates([]);
      setPaidAmount(0);
    }
  };

  const addInstallment = () => {
    const amount = parseFloat(currentInstallment);
    const remaining = totalAmount - paidAmount;
    const today = new Date().toISOString().split('T')[0];
    
    if (!currentEmiDate) {
      setAlertMessage('Please select EMI date');
      onOpen();
      return;
    }
    
    if (currentEmiDate < today) {
      setAlertMessage('EMI date cannot be in the past');
      onOpen();
      return;
    }
    
    if (emiDates.includes(currentEmiDate)) {
      setAlertMessage('This EMI date is already selected');
      onOpen();
      return;
    }
    
    if (!amount || amount <= 0) {
      setAlertMessage('Please enter a valid amount');
      onOpen();
      return;
    }
    
    if (amount > remaining) {
      setAlertMessage(`Amount cannot exceed remaining balance of ₹${remaining}`);
      onOpen();
      return;
    }
    
    const newInstallments = [...installments, amount];
    const newEmiDates = [...emiDates, currentEmiDate];
    setInstallments(newInstallments);
    setEmiDates(newEmiDates);
    setPaidAmount(paidAmount + amount);
    setCurrentInstallment('');
    setCurrentEmiDate('');
  };

  const removeInstallment = (index) => {
    const removedAmount = installments[index];
    const newInstallments = installments.filter((_, i) => i !== index);
    const newEmiDates = emiDates.filter((_, i) => i !== index);
    setInstallments(newInstallments);
    setEmiDates(newEmiDates);
    setPaidAmount(paidAmount - removedAmount);
  };

  const handlePayment = async (e) => {
    e.preventDefault();
    if (!selectedStudent || totalAmount === 0 || installments.length === 0) {
      alert('Please fill all required fields');
      return;
    }

    try {
      setLoading(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/payments/`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studentId: selectedStudent._id || selectedStudent.id,
          totalAmount: totalAmount,
          installments: installments,
          emiDates: emiDates,
          paymentType: paymentData.paymentType,
          description: paymentData.description,
        }),
      });

      if (response.ok) {
        toast({
          title: 'Payment Recorded!',
          description: `Successfully recorded payment of ₹${totalAmount} for ${selectedStudent.name}`,
          status: 'success',
          duration: 5000,
          isClosable: true,
          position: 'top-right',
        });
        setSelectedStudent(null);
        setSearchTerm('');
        setPaymentData({ amount: '', paymentType: '', description: '' });
        setTotalAmount(0);
        setInstallments([]);
        setEmiDates([]);
        setPaidAmount(0);
      } else {
        const errorData = await response.json();
        toast({
          title: 'Payment Failed',
          description: errorData.error || 'Failed to record payment',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error recording payment:', error);
      toast({
        title: 'Network Error',
        description: error.message || 'Failed to connect to server',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
      <Flex direction="column" w="100%" maxW="800px" mx="auto">
        <Text fontSize="2xl" fontWeight="bold" color={textColor} mb="6">
          Payment
        </Text>

        <Card p="6">
          <VStack spacing="6" align="stretch">
            <FormControl>
              <FormLabel color={textColor}>Search Student</FormLabel>
              <Box position="relative" ref={searchRef}>
                <HStack>
                  <Input
                    placeholder="Search by name, roll number, or class"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    isReadOnly={selectedStudent}
                  />
                  {selectedStudent && (
                    <Button
                      size="sm"
                      colorScheme="red"
                      onClick={() => {
                        setSelectedStudent(null);
                        setSearchTerm('');
                      }}
                    >
                      Clear
                    </Button>
                  )}
                </HStack>
                {filteredStudents.length > 0 && (
                  <Box
                    position="absolute"
                    top="100%"
                    left="0"
                    right="0"
                    bg="white"
                    border="1px solid"
                    borderColor="gray.200"
                    borderRadius="md"
                    maxH="200px"
                    overflowY="auto"
                    zIndex="10"
                  >
                    {filteredStudents.map((student) => (
                      <Box
                        key={student.id}
                        p="3"
                        cursor="pointer"
                        _hover={{ bg: 'gray.50' }}
                        onClick={() => handleStudentSelect(student)}
                      >
                        <Text fontWeight="bold">{student.name}</Text>
                        <Text fontSize="sm" color="gray.600">
                          Class: {student.class} | Roll: {student.roll_number}
                        </Text>
                      </Box>
                    ))}
                  </Box>
                )}
              </Box>
            </FormControl>

            {selectedStudent && (
              <Alert status="info">
                <AlertIcon />
                <Box>
                  <Text fontWeight="bold">{selectedStudent.name}</Text>
                  <Text fontSize="sm">
                    Class: {selectedStudent.class} | Roll: {selectedStudent.roll_number}
                  </Text>
                </Box>
              </Alert>
            )}

            <VStack spacing="4" align="stretch">
              <HStack spacing="4">
                <FormControl isRequired>
                  <FormLabel color={textColor}>Total Amount</FormLabel>
                  <Input
                    type="number"
                    placeholder="Enter total amount"
                    value={paymentData.amount}
                    onChange={(e) => setPaymentData({...paymentData, amount: e.target.value})}
                    isDisabled={totalAmount > 0}
                  />
                </FormControl>
                <Button onClick={handleTotalAmountSet} isDisabled={totalAmount > 0 || !paymentData.amount}>
                  Set Amount
                </Button>
              </HStack>

              <FormControl isRequired>
                <FormLabel color={textColor}>Payment Type</FormLabel>
                <Select
                  placeholder="Select payment type"
                  value={paymentData.paymentType}
                  onChange={(e) => setPaymentData({...paymentData, paymentType: e.target.value})}
                >
                  <option value="tuition">Tuition Fee</option>
                  <option value="exam">Exam Fee</option>
                  <option value="transport">Transport Fee</option>
                  <option value="library">Library Fee</option>
                  <option value="other">Other</option>
                </Select>
              </FormControl>

              {totalAmount > 0 && (
                <Box p="4" border="1px solid" borderColor="gray.200" borderRadius="md">
                  <Text fontWeight="bold" mb="3">EMI Details</Text>
                  <Text mb="2">Total Amount: ₹{totalAmount}</Text>
                  <Text mb="2">Paid Amount: ₹{paidAmount}</Text>
                  <Text mb="4" color={paidAmount === totalAmount ? 'green.500' : 'red.500'}>
                    Remaining: ₹{totalAmount - paidAmount}
                  </Text>
                  
                  {installments.map((amount, index) => (
                    <Flex key={index} justify="space-between" align="center" mb="2" p="2" bg="gray.50" borderRadius="md">
                      <Box>
                        <Text>Installment {index + 1}: ₹{amount}</Text>
                        <Text fontSize="sm" color="gray.600">Due Date: {emiDates[index]}</Text>
                      </Box>
                      <Button size="sm" colorScheme="red" onClick={() => removeInstallment(index)}>
                        Remove
                      </Button>
                    </Flex>
                  ))}
                  
                  {paidAmount < totalAmount && (
                    <VStack mt="3" spacing="3">
                      <HStack w="100%">
                        <Input
                          type="number"
                          placeholder={`Enter amount (Remaining: ₹${totalAmount - paidAmount})`}
                          value={currentInstallment}
                          onChange={(e) => setCurrentInstallment(e.target.value)}
                          max={totalAmount - paidAmount}
                        />
                        <Input
                          type="date"
                          placeholder="EMI Due Date"
                          value={currentEmiDate}
                          onChange={(e) => setCurrentEmiDate(e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                        />
                      </HStack>
                      <Button onClick={addInstallment} isDisabled={!currentInstallment || !currentEmiDate} w="100%">
                        Add Installment
                      </Button>
                    </VStack>
                  )}
                  
                  {paidAmount === totalAmount && (
                    <Text color="green.500" fontWeight="bold" textAlign="center">
                      ✓ Full Amount Paid
                    </Text>
                  )}
                </Box>
              )}

              <FormControl>
                <FormLabel color={textColor}>Description</FormLabel>
                <Input
                  placeholder="Payment description (optional)"
                  value={paymentData.description}
                  onChange={(e) => setPaymentData({...paymentData, description: e.target.value})}
                />
              </FormControl>

              <Button
                onClick={handlePayment}
                colorScheme="blue"
                size="lg"
                isLoading={loading}
                isDisabled={!selectedStudent || paidAmount !== totalAmount || installments.length === 0}
              >
                Record Payment
              </Button>
            </VStack>
            
            <AlertDialog
              isOpen={isOpen}
              leastDestructiveRef={cancelRef}
              onClose={onClose}
            >
              <AlertDialogOverlay>
                <AlertDialogContent>
                  <AlertDialogHeader fontSize="lg" fontWeight="bold">
                    Invalid Amount
                  </AlertDialogHeader>
                  <AlertDialogBody>
                    {alertMessage}
                  </AlertDialogBody>
                  <AlertDialogFooter>
                    <Button ref={cancelRef} onClick={onClose}>
                      OK
                    </Button>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialogOverlay>
            </AlertDialog>
          </VStack>
        </Card>
      </Flex>
    </Box>
  );
}