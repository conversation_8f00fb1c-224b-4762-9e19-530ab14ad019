// Install cors package first: npm install cors

const express = require('express');
const cors = require('cors');
const app = express();

// Option 1: Allow all origins (for development only)
app.use(cors());

// Option 2: Allow specific origin (recommended)
app.use(cors({
  origin: 'http://localhost:3001', // Your React app URL
  credentials: true, // If you need to send cookies
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Your existing routes
app.post('/api/users/login', (req, res) => {
  // Your login logic here
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});