import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  VStack,
  HStack,
  Badge,
  Textarea,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Spinner,
  Checkbox,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import Card from 'components/card/Card';
import { useAuth } from 'contexts/AuthContext';

export default function EMIPayment() {
  const { token } = useAuth();
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedEMI, setSelectedEMI] = useState(null);
  const [paymentNote, setPaymentNote] = useState('');
  const [customAmount, setCustomAmount] = useState('');
  const [useCustomAmount, setUseCustomAmount] = useState(false);
  const [deletePaymentId, setDeletePaymentId] = useState(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const toast = useToast();

  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');

  useEffect(() => {
    fetchPayments();
  }, []);

  const fetchPayments = async () => {
    try {
      const response = await fetch('/api/payments', {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      const paymentsData = Array.isArray(data) ? data : (data.payments || []);
      setPayments(paymentsData);
    } catch (error) {
      console.error('Error fetching payments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEMIPayment = (payment, installment) => {
    console.log('EMI Data:', installment);
    const cumulativeRemaining = payment.installments
      ?.filter(emi => emi.installment_number < installment.installment_number && emi.status === 'partial')
      ?.reduce((sum, emi) => sum + (emi.remaining_amount || 0), 0) || 0;
    const remainingAmount = installment.remaining_amount || installment.amount;
    const totalAmount = remainingAmount + cumulativeRemaining;
    
    setSelectedEMI({ payment, installment, cumulativeRemaining });
    setPaymentNote('');
    setCustomAmount(totalAmount.toString());
    setUseCustomAmount(false);
    onOpen();
  };

  const processEMIPayment = async () => {
    const totalAmount = selectedEMI.installment.amount + (selectedEMI.cumulativeRemaining || 0);
    
    const paymentAmount = useCustomAmount ? parseFloat(customAmount) : totalAmount;
    
    if (useCustomAmount && (!customAmount || paymentAmount <= 0)) {
      toast({
        title: 'Invalid Amount',
        description: 'Please enter a valid payment amount',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/admin/emi-payment', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentId: selectedEMI.payment._id || selectedEMI.payment.paymentId,
          paidAmount: paymentAmount,
          notes: paymentNote || '',
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const requiredAmount = selectedEMI.installment.amount + (selectedEMI.cumulativeRemaining || 0);
        const extraAmount = paymentAmount - requiredAmount;
        
        let description = `₹${paymentAmount} paid for EMI ${selectedEMI.installment.installment_number}`;
        
        if (extraAmount > 0) {
          description += `. Extra amount: ₹${extraAmount.toFixed(2)} (adjusted for future EMIs)`;
        } else if (result.nextEMIUpdated && result.remainingAmount > 0) {
          description += `. ₹${result.remainingAmount} added to next EMI`;
        }
        
        toast({
          title: 'EMI Payment Successful',
          description: description,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        // Refresh payments to show updated amounts
        await fetchPayments();
        onClose();
        
        // Show extra amount in a separate toast if there's overpayment
        if (extraAmount > 0) {
          setTimeout(() => {
            toast({
              title: 'Extra Payment Processed',
              description: `₹${extraAmount.toFixed(2)} extra amount will reduce future EMI payments`,
              status: 'info',
              duration: 4000,
              isClosable: true,
            });
          }, 1000);
        }
      }
    } catch (error) {
      toast({
        title: 'Payment Failed',
        description: 'Failed to process EMI payment',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePayment = (paymentId) => {
    setDeletePaymentId(paymentId);
    onDeleteOpen();
  };

  const confirmDeletePayment = async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:3001/admin/payment/${deletePaymentId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        toast({
          title: 'Payment Deleted',
          description: 'Payment record deleted successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        fetchPayments();
        onDeleteClose();
      }
    } catch (error) {
      toast({
        title: 'Delete Failed',
        description: 'Failed to delete payment record',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'green';
      case 'overdue': return 'red';
      case 'pending': return 'orange';
      case 'partial': return 'blue';
      default: return 'gray';
    }
  };

  if (loading) {
    return (
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
        <Flex justifyContent="center" align="center" h="200px">
          <Spinner size="xl" />
        </Flex>
      </Box>
    );
  }

  return (
    <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
      <Text fontSize="2xl" fontWeight="bold" color={textColor} mb="6">
        EMI Payment Management
      </Text>

      <VStack spacing="6" align="stretch">
        {payments.map((payment) => (
          <Card key={payment._id} p="6">
            <VStack align="stretch" spacing="4">
              <HStack justify="space-between">
                <VStack align="start" spacing="1">
                  <Text fontSize="lg" fontWeight="bold" color={textColor}>
                    {payment.student_id?.name || 'N/A'}
                  </Text>
                  <Text fontSize="sm" color="gray.500">
                    {payment.payment_type} - Total: ₹{payment.total_amount}
                  </Text>
                </VStack>
                {payment.emiSummary && (
                  <VStack align="end" spacing="1">
                    <Text fontSize="sm" color={textColor}>
                      {payment.emiSummary.paid_installments}/{payment.emiSummary.total_installments} EMIs Paid
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      EMI Total: ₹{payment.installments?.reduce((sum, emi) => sum + (emi.amount || 0), 0) || payment.total_amount}
                    </Text>
                    <Text fontSize="sm" color="green.500">
                      Paid: ₹{payment.emiSummary.total_paid}
                    </Text>
                    {payment.emiSummary.total_paid > payment.total_amount && (
                      <Text fontSize="sm" color="blue.500" fontWeight="bold">
                        Extra amount: ₹{payment.emiSummary.total_paid - payment.total_amount}
                      </Text>
                    )}
                    <Text fontSize="sm" color={payment.emiSummary.total_remaining > 0 ? "red.500" : "green.500"}>
                      Remaining: ₹{payment.emiSummary.total_remaining}
                    </Text>
                  </VStack>
                )}
              </HStack>

              <HStack justify="space-between" mb="4">
                <Text fontSize="md" fontWeight="semibold">EMI Details</Text>
                <Button
                  size="sm"
                  colorScheme="red"
                  variant="outline"
                  onClick={() => handleDeletePayment(payment._id || payment.paymentId)}
                >
                  Delete Payment
                </Button>
              </HStack>
              <Table size="sm" variant="simple">
                <Thead>
                  <Tr>
                    <Th borderColor={borderColor}>EMI #</Th>
                    <Th borderColor={borderColor}>Amount</Th>
                    <Th borderColor={borderColor}>Remaining</Th>
                    <Th borderColor={borderColor}>Due Date</Th>
                    <Th borderColor={borderColor}>Status</Th>
                    <Th borderColor={borderColor}>Paid Date</Th>
                    <Th borderColor={borderColor}>Note</Th>
                    <Th borderColor={borderColor}>Action</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {payment.installments?.map((installment) => (
                    <Tr key={installment._id}>
                      <Td borderColor="transparent">
                        <Text fontWeight="bold">EMI {installment.installment_number}</Text>
                      </Td>
                      <Td borderColor="transparent">
                        <Text>₹{installment.amount}</Text>
                      </Td>
                      <Td borderColor="transparent">
                        <Text color="red.500">
                          ₹{installment.status === 'paid' ? 0 : (installment.remaining_amount || installment.amount)}
                        </Text>
                      </Td>
                      <Td borderColor="transparent">
                        <Text fontSize="sm">
                          {new Date(installment.due_date).toLocaleDateString()}
                        </Text>
                      </Td>
                      <Td borderColor="transparent">
                        <Badge colorScheme={getStatusColor(installment.status)}>
                          {installment.status}
                        </Badge>
                      </Td>
                      <Td borderColor="transparent">
                        <Text fontSize="sm">
                          {installment.paid_date 
                            ? new Date(installment.paid_date).toLocaleString()
                            : '-'
                          }
                        </Text>
                      </Td>
                      <Td borderColor="transparent">
                        <Text fontSize="sm" noOfLines={2}>
                          {installment.notes || installment.note || installment.payment_note || '-'}
                        </Text>
                      </Td>
                      <Td borderColor="transparent">
                        {installment.status === 'pending' || installment.status === 'overdue' || installment.status === 'partial' ? (
                          <Button
                            size="sm"
                            colorScheme="green"
                            onClick={() => handleEMIPayment(payment, installment)}
                          >
                            Pay EMI
                          </Button>
                        ) : (
                          <Text fontSize="sm" color="green.500">Paid</Text>
                        )}
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </VStack>
          </Card>
        ))}
      </VStack>

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Pay EMI</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedEMI && (
              <VStack spacing="4" align="stretch">
                <Text>
                  <strong>Student:</strong> {selectedEMI.payment.student_id?.name}
                </Text>
                <Text>
                  <strong>EMI:</strong> {selectedEMI.installment.installment_number}
                </Text>
                <Text>
                  <strong>EMI Amount:</strong> ₹{selectedEMI.installment.amount}
                </Text>
                {selectedEMI.installment.remaining_amount > 0 && selectedEMI.installment.status === 'partial' && (
                  <Text fontSize="sm" color="red.600">
                    <strong>Remaining Amount:</strong> ₹{selectedEMI.installment.remaining_amount}
                  </Text>
                )}
                {selectedEMI.cumulativeRemaining > 0 && (
                  <Text fontSize="sm" color="blue.600">
                    <strong>Previous EMI Remaining:</strong> ₹{selectedEMI.cumulativeRemaining}
                  </Text>
                )}
                <Text>
                  <strong>Due Date:</strong> {new Date(selectedEMI.installment.due_date).toLocaleDateString()}
                </Text>
                
                <FormControl>
                  <Checkbox 
                    isChecked={useCustomAmount}
                    onChange={(e) => setUseCustomAmount(e.target.checked)}
                  >
                    Use custom payment amount
                  </Checkbox>
                </FormControl>

                {useCustomAmount && (
                  <FormControl>
                    <FormLabel>Custom Payment Amount</FormLabel>
                    <Input
                      type="number"
                      placeholder="Enter custom amount"
                      value={customAmount}
                      onChange={(e) => setCustomAmount(e.target.value)}
                    />
                    {parseFloat(customAmount) > (selectedEMI.installment.amount + (selectedEMI.cumulativeRemaining || 0)) && (
                      <Alert status="info" mt={2}>
                        <AlertIcon />
                        <VStack align="start" spacing={1}>
                          <Text fontWeight="bold">Extra Amount: ₹{(parseFloat(customAmount) - (selectedEMI.installment.amount + (selectedEMI.cumulativeRemaining || 0))).toFixed(2)}</Text>
                          <Text fontSize="sm">This extra amount will be adjusted in future EMIs</Text>
                        </VStack>
                      </Alert>
                    )}
                    {parseFloat(customAmount) < (selectedEMI.installment.remaining_amount || selectedEMI.installment.amount) && parseFloat(customAmount) > 0 && (
                      <Alert status="info" mt={2}>
                        <AlertIcon />
                        Partial Payment: ₹{(selectedEMI.installment.remaining_amount || selectedEMI.installment.amount) - parseFloat(customAmount)} will remain unpaid
                      </Alert>
                    )}
                  </FormControl>
                )}
                
                <FormControl>
                  <FormLabel>Payment Note</FormLabel>
                  <Textarea
                    placeholder="Add payment note (optional)"
                    value={paymentNote}
                    onChange={(e) => setPaymentNote(e.target.value)}
                    rows={3}
                  />
                </FormControl>

                <Box p={3} bg="gray.50" borderRadius="md">
                  <Text fontWeight="bold">
                    Payment Amount: ₹{useCustomAmount ? (customAmount || 0) : ((selectedEMI.installment.remaining_amount || selectedEMI.installment.amount) + (selectedEMI.cumulativeRemaining || 0))}
                  </Text>
                  {selectedEMI.cumulativeRemaining > 0 && (
                    <Text fontSize="sm" color="blue.600" mt={1}>
                      Original Amount: ₹{selectedEMI.installment.amount} + Previous EMI Remaining: ₹{selectedEMI.cumulativeRemaining}
                    </Text>
                  )}
                  {useCustomAmount && parseFloat(customAmount) < (selectedEMI.installment.remaining_amount || selectedEMI.installment.amount) && parseFloat(customAmount) > 0 && (
                    <Text fontSize="sm" color="orange.600" mt={1}>
                      Remaining ₹{(selectedEMI.installment.remaining_amount || selectedEMI.installment.amount) - parseFloat(customAmount)} will remain unpaid
                    </Text>
                  )}
                  {useCustomAmount && parseFloat(customAmount) > (selectedEMI.installment.amount + (selectedEMI.cumulativeRemaining || 0)) && (
                    <Text fontSize="sm" color="green.600" mt={1} fontWeight="bold">
                      Extra Amount: ₹{(parseFloat(customAmount) - (selectedEMI.installment.amount + (selectedEMI.cumulativeRemaining || 0))).toFixed(2)} (will be adjusted in future EMIs)
                    </Text>
                  )}
                </Box>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="green" mr={3} onClick={processEMIPayment} isLoading={loading}>
              Confirm Payment
            </Button>
            <Button variant="ghost" onClick={onClose}>
              Cancel
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Delete Payment</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>Are you sure you want to delete this payment record? This action cannot be undone.</Text>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="red" mr={3} onClick={confirmDeletePayment} isLoading={loading}>
              Delete
            </Button>
            <Button variant="ghost" onClick={onDeleteClose}>
              Cancel
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
}