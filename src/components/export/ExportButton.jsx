import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MenuList,
  MenuItem,
  useToast,
  Text
} from '@chakra-ui/react';
import { FiDownload, FiChevronDown } from 'react-icons/fi';
import { useAuth } from 'contexts/AuthContext';

export default function ExportButton({ filters = {}, onExportStart }) {
  const { token } = useAuth();
  const toast = useToast();
  const [loading, setLoading] = useState(false);

  const exportStudents = async (limit = 1000) => {
    try {
      setLoading(true);
      if (onExportStart) onExportStart();

      const exportData = {
        filters,
        options: { limit, sortBy: 'name', sortOrder: 'asc' },
        exportType: 'streaming'
      };
      
      console.log('Export request:', exportData);

      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/export/excel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(exportData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Export failed: ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.success || !data.filename) {
        throw new Error('Export failed: No filename returned');
      }

      // Download the file using the filename
      const downloadResponse = await fetch(`${baseUrl}/api/students/export/download/${data.filename}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!downloadResponse.ok) {
        throw new Error('Failed to download export file');
      }

      const blob = await downloadResponse.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = data.filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Export Successful',
        description: `${data.recordCount} students exported successfully`,
        status: 'success',
        duration: 3000
      });
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Menu>
      <MenuButton
        as={Button}
        rightIcon={<FiChevronDown />}
        leftIcon={<FiDownload />}
        size="sm"
        variant="outline"
        isLoading={loading}
      >
        Export
      </MenuButton>
      <MenuList>
        <MenuItem onClick={() => exportStudents(100)}>
          Export 100 records
        </MenuItem>
        <MenuItem onClick={() => exportStudents(1000)}>
          Export 1000 records
        </MenuItem>
        <MenuItem onClick={() => exportStudents(5000)}>
          Export 5000 records
        </MenuItem>
      </MenuList>
    </Menu>
  );
}