import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  Progress,
  Text,
  VStack,
  useToast,
  Badge
} from '@chakra-ui/react';
import { useAuth } from 'contexts/AuthContext';

export default function BackgroundExport({ isOpen, onClose, filters = {} }) {
  const { token } = useAuth();
  const toast = useToast();
  const [jobId, setJobId] = useState(null);
  const [status, setStatus] = useState(null);
  const [progress, setProgress] = useState(0);

  const startExport = async () => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/export/background`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          filters,
          options: { sortBy: 'name', sortOrder: 'asc' }
        })
      });

      const data = await response.json();
      if (data.success) {
        setJobId(data.jobId);
        setStatus('processing');
      }
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    }
  };

  const checkStatus = async () => {
    if (!jobId) return;

    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/export/status/${jobId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const data = await response.json();
      setStatus(data.status);
      setProgress(data.progress || 0);

      if (data.status === 'completed' && data.filename) {
        downloadFile(data.filename);
      }
    } catch (error) {
      console.error('Status check failed:', error);
    }
  };

  const downloadFile = async (filename) => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/export/download/${filename}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Download Complete',
        description: 'Export file downloaded successfully',
        status: 'success',
        duration: 3000
      });
    } catch (error) {
      toast({
        title: 'Download Failed',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    }
  };

  useEffect(() => {
    if (status === 'processing') {
      const interval = setInterval(checkStatus, 2000);
      return () => clearInterval(interval);
    }
  }, [status, jobId]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} closeOnOverlayClick={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Background Export</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing="4">
            {!jobId && (
              <Text>Start a background export for large datasets</Text>
            )}
            
            {status && (
              <VStack spacing="3" w="full">
                <Badge colorScheme={status === 'completed' ? 'green' : 'blue'}>
                  {status.toUpperCase()}
                </Badge>
                
                {status === 'processing' && (
                  <>
                    <Progress value={progress} w="full" colorScheme="blue" />
                    <Text fontSize="sm">{progress}% Complete</Text>
                  </>
                )}
                
                {status === 'completed' && (
                  <Text color="green.500">Export completed successfully!</Text>
                )}
              </VStack>
            )}
          </VStack>
        </ModalBody>
        <ModalFooter>
          {!jobId ? (
            <Button colorScheme="blue" onClick={startExport}>
              Start Export
            </Button>
          ) : (
            <Button onClick={onClose}>
              Close
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}