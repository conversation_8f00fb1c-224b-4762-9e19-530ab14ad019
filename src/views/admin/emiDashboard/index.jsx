import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Flex,
  FormControl,
  Input,
  Text,
  useColorModeValue,
  VStack,
  HStack,
  Badge,
  Textarea,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Spinner,
  Alert,
  AlertIcon,
  Select,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  Grid,
  GridItem,
  Icon,
  Avatar,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
} from '@chakra-ui/react';
import { FiUsers, FiDollarSign, FiClock, FiTrendingUp, FiRefreshCw, FiDownload } from 'react-icons/fi';
import Card from 'components/card/Card';
import { useAuth } from 'contexts/AuthContext';

export default function EMIDashboard() {
  const { token } = useAuth();
  const [students, setStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [paymentTimeline, setPaymentTimeline] = useState([]);
  const [cumulativeData, setCumulativeData] = useState([]);
  const [actualPayments, setActualPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterClass, setFilterClass] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [paymentAmount, setPaymentAmount] = useState('');
  const [paymentNote, setPaymentNote] = useState('');
  const [refundAmount, setRefundAmount] = useState('');
  const [refundReason, setRefundReason] = useState('');
  const [adjustmentAmount, setAdjustmentAmount] = useState('');
  const [adjustmentType, setAdjustmentType] = useState('credit');
  const [adjustmentReason, setAdjustmentReason] = useState('');
  const [timelineFilter, setTimelineFilter] = useState('active');
  const [deleteTimelinePaymentId, setDeleteTimelinePaymentId] = useState(null);
  const [dashboardStats, setDashboardStats] = useState({
    totalStudents: 0,
    totalPayments: 0,
    pendingPayments: 0,
    totalExtraAmount: 0
  });

  const { isOpen: isPaymentOpen, onOpen: onPaymentOpen, onClose: onPaymentClose } = useDisclosure();
  const { isOpen: isTimelineOpen, onOpen: onTimelineOpen, onClose: onTimelineClose } = useDisclosure();
  const { isOpen: isRefundOpen, onOpen: onRefundOpen, onClose: onRefundClose } = useDisclosure();
  const { isOpen: isAdjustmentOpen, onOpen: onAdjustmentOpen, onClose: onAdjustmentClose } = useDisclosure();
  const { isOpen: isCumulativeOpen, onOpen: onCumulativeOpen, onClose: onCumulativeClose } = useDisclosure();
  const { isOpen: isActualPaymentsOpen, onOpen: onActualPaymentsOpen, onClose: onActualPaymentsClose } = useDisclosure();
  const { isOpen: isDeleteTimelineOpen, onOpen: onDeleteTimelineOpen, onClose: onDeleteTimelineClose } = useDisclosure();
  const cancelRef = React.useRef();
  const toast = useToast();

  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const paymentsRes = await fetch('/api/payments', {
        headers: { Authorization: `Bearer ${token}` }
      });

      const paymentsData = await paymentsRes.json();
      const payments = Array.isArray(paymentsData) ? paymentsData : paymentsData.payments || [];
      
      setStudents(payments);
      
      const totalPayments = payments.reduce((sum, p) => sum + (p.emiSummary?.total_paid || 0), 0);
      const pendingPayments = payments.filter(p => p.emiSummary?.total_remaining > 0).length;
      const totalExtraAmount = payments.reduce((sum, p) => {
        const extra = p.installments?.reduce((extraSum, emi) => {
          return extraSum + (emi.paid_amount > emi.amount ? emi.paid_amount - emi.amount : 0);
        }, 0) || 0;
        return sum + extra;
      }, 0);

      setDashboardStats({
        totalStudents: payments.length || 0,
        totalPayments,
        pendingPayments,
        totalExtraAmount
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePayEMI = (student) => {
    setSelectedStudent(student);
    setPaymentAmount('');
    setPaymentNote('');
    onPaymentOpen();
  };

  const handleRefund = (student) => {
    setSelectedStudent(student);
    setRefundAmount('');
    setRefundReason('');
    onRefundOpen();
  };

  const handleAdjustment = (student) => {
    setSelectedStudent(student);
    setAdjustmentAmount('');
    setAdjustmentReason('');
    onAdjustmentOpen();
  };

  const processRefund = async () => {
    if (!selectedStudent || !refundAmount) {
      toast({ title: 'Invalid Input', description: 'Please enter refund amount', status: 'error', duration: 3000, isClosable: true });
      return;
    }
    try {
      setLoading(true);
      const response = await fetch('/admin/refund', {
        method: 'POST',
        headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
        body: JSON.stringify({
          student_id: selectedStudent.student_id?._id || selectedStudent._id,
          amount: parseFloat(refundAmount),
          reason: refundReason
        })
      });
      if (response.ok) {
        toast({ title: 'Refund Processed', description: `₹${refundAmount} refund processed successfully`, status: 'success', duration: 5000, isClosable: true });
        await fetchDashboardData();
        onRefundClose();
      }
    } catch (error) {
      toast({ title: 'Refund Failed', description: 'Failed to process refund', status: 'error', duration: 3000, isClosable: true });
    } finally {
      setLoading(false);
    }
  };

  const generateReceipt = async (student) => {
    try {
      setLoading(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/payments/${student._id}/receipt`, {
        method: 'POST',
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Download the receipt
          const downloadResponse = await fetch(`${baseUrl}/api/payments/receipt/download/${data.filename}`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          if (downloadResponse.ok) {
            const blob = await downloadResponse.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            toast({
              title: 'Receipt Generated',
              description: `Receipt ${data.receiptNumber} downloaded successfully`,
              status: 'success',
              duration: 3000
            });
          }
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate receipt');
      }
    } catch (error) {
      toast({
        title: 'Receipt Generation Failed',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const generateEmiStatus = async (student) => {
    try {
      setLoading(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const studentId = student.student_id?._id || student._id;
      const response = await fetch(`${baseUrl}/api/students/emi-status/${studentId}/pdf`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `emi_status_${student.student_id?.name || 'student'}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast({
          title: 'EMI Status Generated',
          description: 'EMI Status PDF downloaded successfully',
          status: 'success',
          duration: 3000
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate EMI status');
      }
    } catch (error) {
      toast({
        title: 'EMI Status Generation Failed',
        description: error.message,
        status: 'error',
        duration: 3000
      });
    } finally {
      setLoading(false);
    }
  };

  const processAdjustment = async () => {
    if (!selectedStudent || !adjustmentAmount) {
      toast({ title: 'Invalid Input', description: 'Please enter adjustment amount', status: 'error', duration: 3000, isClosable: true });
      return;
    }
    try {
      setLoading(true);
      const response = await fetch('/admin/adjustment', {
        method: 'POST',
        headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
        body: JSON.stringify({
          student_id: selectedStudent.student_id?._id || selectedStudent._id,
          amount: parseFloat(adjustmentAmount),
          adjustment_type: adjustmentType,
          reason: adjustmentReason
        })
      });
      if (response.ok) {
        toast({ title: 'Adjustment Processed', description: `₹${adjustmentAmount} ${adjustmentType} adjustment processed`, status: 'success', duration: 5000, isClosable: true });
        await fetchDashboardData();
        onAdjustmentClose();
      }
    } catch (error) {
      toast({ title: 'Adjustment Failed', description: 'Failed to process adjustment', status: 'error', duration: 3000, isClosable: true });
    } finally {
      setLoading(false);
    }
  };

  const fetchPaymentTimeline = async (studentId) => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/actual-payments/${studentId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      const data = await response.json();
      setPaymentTimeline(Array.isArray(data) ? data : data.timeline || []);
    } catch (error) {
      console.error('Error fetching timeline:', error);
      setPaymentTimeline([]);
    }
  };

  const handleDeleteTimelinePayment = async () => {
    try {
      setLoading(true);
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/payments/transaction/${deleteTimelinePaymentId}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.ok) {
        toast({
          title: 'Payment Deleted',
          description: 'Payment has been deleted from timeline',
          status: 'success',
          duration: 3000,
          isClosable: true
        });
        // Refresh timeline and dashboard data
        await fetchPaymentTimeline(selectedStudent.student_id?._id || selectedStudent._id);
        await fetchDashboardData();
      } else {
        throw new Error('Failed to delete payment');
      }
    } catch (error) {
      toast({
        title: 'Delete Failed',
        description: error.message || 'Failed to delete payment',
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    } finally {
      setLoading(false);
      onDeleteTimelineClose();
      setDeleteTimelinePaymentId(null);
    }
  };

  const handleViewTimeline = async (student) => {
    setSelectedStudent(student);
    await fetchPaymentTimeline(student.student_id?._id || student._id);
    onTimelineOpen();
  };

  const handleViewCumulative = async (student) => {
    setSelectedStudent(student);
    await fetchCumulativeData(student.student_id?._id || student._id);
    onCumulativeOpen();
  };

  const handleViewActualPayments = async (student) => {
    setSelectedStudent(student);
    await fetchActualPayments(student.student_id?._id || student._id);
    onActualPaymentsOpen();
  };

  const fetchActualPayments = async (studentId) => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/api/students/actual-payments/${studentId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      const data = await response.json();
      setActualPayments(Array.isArray(data) ? data : data.payments || []);
    } catch (error) {
      console.error('Error fetching actual payments:', error);
      setActualPayments([]);
    }
  };

  const fetchCumulativeData = async (studentId) => {
    try {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
      const url = `${baseUrl}/api/students/payment-history-cumulative/${studentId}`;
      console.log('Fetching cumulative data from:', url);
      
      const response = await fetch(url, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('Response status:', response.status);
      const data = await response.json();
      console.log('Cumulative data received:', data);
      
      setCumulativeData(Array.isArray(data) ? data : data.payments || []);
    } catch (error) {
      console.error('Error fetching cumulative data:', error);
      setCumulativeData([]);
      toast({
        title: 'Error',
        description: 'Failed to fetch cumulative data',
        status: 'error',
        duration: 3000
      });
    }
  };

  const processPayment = async () => {
    if (!selectedStudent || !paymentAmount) {
      toast({
        title: 'Invalid Input',
        description: 'Please enter a valid payment amount',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const amount = parseFloat(paymentAmount);
    const remainingBalance = selectedStudent.emiSummary?.total_remaining || 0;

    if (amount > remainingBalance && remainingBalance > 0) {
      toast({
        title: 'Invalid Amount',
        description: `Amount cannot exceed remaining balance of ₹${remainingBalance}`,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/admin/emi-payment', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentId: selectedStudent._id,
          paidAmount: amount,
          notes: paymentNote || '',
        }),
      });

      if (response.ok) {
        const extraAmount = amount - remainingBalance;
        let description = `₹${amount} payment processed successfully`;
        if (extraAmount > 0) {
          description += `. Extra amount: ₹${extraAmount.toFixed(2)} recorded`;
        }

        toast({
          title: 'Payment Successful',
          description,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        await fetchDashboardData();
        onPaymentClose();
      }
    } catch (error) {
      toast({
        title: 'Payment Failed',
        description: 'Failed to process payment',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.student_id?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.student_id?.admission_number?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = !filterClass || student.student_id?.class === filterClass;
    const matchesStatus = !filterStatus || 
      (filterStatus === 'paid' && student.emiSummary?.total_remaining === 0) ||
      (filterStatus === 'pending' && student.emiSummary?.total_remaining > 0) ||
      (filterStatus === 'partial' && student.emiSummary?.total_paid > 0 && student.emiSummary?.total_remaining > 0);
    
    return matchesSearch && matchesClass && matchesStatus;
  });

  const getStatusColor = (student) => {
    if (!student.emiSummary) return 'gray';
    if (student.emiSummary.total_remaining === 0) return 'green';
    if (student.emiSummary.total_paid > 0) return 'orange';
    return 'red';
  };

  const getStatusText = (student) => {
    if (!student.emiSummary) return 'No Data';
    if (student.emiSummary.total_remaining === 0) return 'Paid';
    if (student.emiSummary.total_paid > 0) return 'Partial';
    return 'Pending';
  };

  if (loading) {
    return (
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
        <Flex justifyContent="center" align="center" h="200px">
          <Spinner size="xl" />
        </Flex>
      </Box>
    );
  }

  return (
    <>
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }} h="100vh" display="flex" flexDirection="column">
        {/* Sticky Header Section */}
        <Box position="sticky" top="0" zIndex="10" bg="white" pb="4">
          {/* Header */}
          <HStack justify="space-between" mb="6">
            <Button
              leftIcon={<Icon as={FiRefreshCw} />}
              colorScheme="blue"
              onClick={fetchDashboardData}
              isLoading={loading}
            >
              Refresh
            </Button>
          </HStack>

          {/* Dashboard Stats */}
          <Grid templateColumns="repeat(4, 1fr)" gap="6" mb="6">
            <GridItem>
              <Card p="6">
                <Stat>
                  <HStack>
                    <Box p="3" bg="blue.100" borderRadius="lg">
                      <Icon as={FiUsers} color="blue.500" boxSize="6" />
                    </Box>
                    <VStack align="start" spacing="0">
                      <StatNumber fontSize="2xl" color={textColor}>
                        {dashboardStats.totalStudents}
                      </StatNumber>
                      <StatLabel color="gray.500">Total Students</StatLabel>
                    </VStack>
                  </HStack>
                </Stat>
              </Card>
            </GridItem>
            
            <GridItem>
              <Card p="6">
                <Stat>
                  <HStack>
                    <Box p="3" bg="green.100" borderRadius="lg">
                      <Icon as={FiDollarSign} color="green.500" boxSize="6" />
                    </Box>
                    <VStack align="start" spacing="0">
                      <StatNumber fontSize="2xl" color={textColor}>
                        ₹{dashboardStats.totalPayments.toLocaleString()}
                      </StatNumber>
                      <StatLabel color="gray.500">Total Payments</StatLabel>
                    </VStack>
                  </HStack>
                </Stat>
              </Card>
            </GridItem>
            
            <GridItem>
              <Card p="6">
                <Stat>
                  <HStack>
                    <Box p="3" bg="orange.100" borderRadius="lg">
                      <Icon as={FiClock} color="orange.500" boxSize="6" />
                    </Box>
                    <VStack align="start" spacing="0">
                      <StatNumber fontSize="2xl" color={textColor}>
                        {dashboardStats.pendingPayments}
                      </StatNumber>
                      <StatLabel color="gray.500">Pending Payments</StatLabel>
                    </VStack>
                  </HStack>
                </Stat>
              </Card>
            </GridItem>
            
            <GridItem>
              <Card p="6">
                <Stat>
                  <HStack>
                    <Box p="3" bg="purple.100" borderRadius="lg">
                      <Icon as={FiTrendingUp} color="purple.500" boxSize="6" />
                    </Box>
                    <VStack align="start" spacing="0">
                      <StatNumber fontSize="2xl" color={textColor}>
                        ₹{dashboardStats.totalExtraAmount.toLocaleString()}
                      </StatNumber>
                      <StatLabel color="gray.500">Extra Amounts</StatLabel>
                    </VStack>
                  </HStack>
                </Stat>
              </Card>
            </GridItem>
          </Grid>

          {/* Filters */}
          <Card p="6">
            <HStack spacing="4" wrap="wrap">
              <FormControl maxW="300px">
                <Input
                  placeholder="Search students..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </FormControl>
              
              <FormControl maxW="150px">
                <Select
                  placeholder="All Classes"
                  value={filterClass}
                  onChange={(e) => setFilterClass(e.target.value)}
                >
                  <option value="10th">10th</option>
                  <option value="11th">11th</option>
                  <option value="12th">12th</option>
                </Select>
              </FormControl>
              
              <FormControl maxW="150px">
                <Select
                  placeholder="All Status"
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <option value="paid">Paid</option>
                  <option value="partial">Partial</option>
                  <option value="pending">Pending</option>
                </Select>
              </FormControl>
              
              <Button leftIcon={<Icon as={FiDownload} />} variant="outline">
                Export
              </Button>
            </HStack>
          </Card>
        </Box>

        {/* Scrollable Students Table */}
        <Card p="0" overflow="hidden" flex="1" display="flex" flexDirection="column">
          <Box p="6" bg="gray.50" borderBottom="1px" borderColor={borderColor} position="sticky" top="0" zIndex="5">
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                Student Payment Overview
              </Text>
            </Box>
            
            <Box flex="1" overflowY="auto">
              <Table variant="simple">
              <Thead bg="gray.100" position="sticky" top="0" zIndex="4">
                <Tr>
                  <Th>Student</Th>
                  <Th>Class</Th>
                  <Th>Total EMI</Th>
                  <Th>Paid</Th>
                  <Th>Extra Amount</Th>
                  <Th>Remaining</Th>
                  <Th>Status</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredStudents.map((student) => {
                  const extraAmount = student.installments?.reduce((sum, emi) => {
                    return sum + (emi.paid_amount > emi.amount ? emi.paid_amount - emi.amount : 0);
                  }, 0) || 0;
                  
                  return (
                    <Tr key={student._id} _hover={{ bg: "gray.50" }}>
                      <Td>
                        <HStack>
                          <Avatar size="sm" name={student.student_id?.name} src={student.student_id?.photo} />
                          <VStack align="start" spacing="0">
                            <Text fontWeight="semibold">{student.student_id?.name}</Text>
                            <Text fontSize="sm" color="gray.500">
                              {student.student_id?.admission_number}
                            </Text>
                          </VStack>
                        </HStack>
                      </Td>
                      <Td>
                        <Badge colorScheme="blue" variant="subtle">
                          {student.student_id?.class} {student.student_id?.section}
                        </Badge>
                      </Td>
                      <Td>
                        <Text fontWeight="bold">₹{student.total_amount || 0}</Text>
                      </Td>
                      <Td>
                        <Text color="green.500" fontWeight="semibold">
                          ₹{student.emiSummary?.total_paid || 0}
                        </Text>
                      </Td>
                      <Td>
                        {extraAmount > 0 ? (
                          <Badge colorScheme="orange" variant="solid">
                            ₹{extraAmount}
                          </Badge>
                        ) : (
                          <Text color="gray.400">₹0</Text>
                        )}
                      </Td>
                      <Td>
                        <Text color="red.500" fontWeight="semibold">
                          ₹{student.emiSummary?.total_remaining || 0}
                        </Text>
                      </Td>
                      <Td>
                        <Badge colorScheme={getStatusColor(student)} variant="solid">
                          {getStatusText(student)}
                        </Badge>
                      </Td>
                      <Td>
                        <VStack spacing="2" align="start">
                          {/* First Line: Receipt, Actual Payment, Timeline */}
                          <HStack spacing="2">
                            <Button 
                              size="sm" 
                              colorScheme="orange" 
                              variant="solid" 
                              onClick={() => generateReceipt(student)}
                              isDisabled={getStatusText(student) === 'Pending'}
                            >
                              Receipt
                            </Button>
                            <Button size="sm" colorScheme="green" variant="solid" onClick={() => handleViewActualPayments(student)}>
                              Actual Payment
                            </Button>
                            <Button size="sm" colorScheme="purple" variant="solid" onClick={() => handleViewTimeline(student)}>
                              Timeline
                            </Button>
                          </HStack>
                          {/* Second Line: Other Options */}
                          <HStack spacing="2">
                            <Button size="sm" colorScheme="teal" variant="outline" onClick={() => generateEmiStatus(student)}>
                              EMI Status
                            </Button>
                            <Button size="sm" colorScheme="red" variant="outline" onClick={() => handleRefund(student)} isDisabled>
                              Refund
                            </Button>
                            <Button size="sm" colorScheme="blue" variant="outline" onClick={() => handleAdjustment(student)} isDisabled>
                              Adjust
                            </Button>
                            <Button size="sm" colorScheme="gray" variant="outline" onClick={() => handleViewCumulative(student)} isDisabled>
                              Cumulative
                            </Button>
                          </HStack>
                        </VStack>
                      </Td>
                    </Tr>
                  );
                })}
              </Tbody>
              </Table>
            </Box>
          </Card>
        </Box>

        {/* Payment Modal */}
      <Modal isOpen={isPaymentOpen} onClose={onPaymentClose} size="xl">
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(10px)" />
        <ModalContent borderRadius="2xl" overflow="hidden">
          <ModalHeader bg="gradient(to-r, green.500, blue.600)" color="white" py="6">
            <HStack>
              <Box w="3" h="3" bg="white" borderRadius="full" />
              <Text fontSize="xl" fontWeight="bold">Process EMI Payment</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton color="white" />
          <ModalBody p="8">
            {selectedStudent && (
              <VStack spacing="6" align="stretch">
                {/* Student Info */}
                <Box p="6" bg="gray.50" borderRadius="xl" border="1px" borderColor="gray.200">
                  <HStack>
                    <Avatar size="lg" name={selectedStudent.name} />
                    <VStack align="start" spacing="1">
                      <Text fontSize="xl" fontWeight="bold">{selectedStudent.name}</Text>
                      <Text color="gray.600">Class: {selectedStudent.class} {selectedStudent.section}</Text>
                      <Text color="gray.600">Admission: {selectedStudent.admission_number}</Text>
                    </VStack>
                  </HStack>
                </Box>

                {/* EMI Summary */}
                <Box p="6" bg="white" borderRadius="xl" border="1px" borderColor="gray.200" shadow="sm">
                  <Text fontSize="md" fontWeight="bold" color="gray.800" mb="4">EMI Summary</Text>
                  <Grid templateColumns="repeat(3, 1fr)" gap="4">
                    <VStack>
                      <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                        ₹{selectedStudent.total_amount || 0}
                      </Text>
                      <Text fontSize="sm" color="gray.600">Total EMI</Text>
                    </VStack>
                    <VStack>
                      <Text fontSize="2xl" fontWeight="bold" color="green.500">
                        ₹{selectedStudent.emiSummary?.total_paid || 0}
                      </Text>
                      <Text fontSize="sm" color="gray.600">Paid</Text>
                    </VStack>
                    <VStack>
                      <Text fontSize="2xl" fontWeight="bold" color="red.500">
                        ₹{selectedStudent.emiSummary?.total_remaining || 0}
                      </Text>
                      <Text fontSize="sm" color="gray.600">Remaining</Text>
                    </VStack>
                  </Grid>
                </Box>

                {/* Payment Input */}
                <Box p="6" bg="white" borderRadius="xl" border="1px" borderColor="gray.200" shadow="sm">
                  <FormControl>
                    <Text fontWeight="bold" color="gray.800" mb="2">Payment Amount</Text>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      value={paymentAmount}
                      onChange={(e) => setPaymentAmount(e.target.value)}
                      size="lg"
                      borderRadius="xl"
                      border="2px"
                      borderColor="gray.200"
                      _focus={{ borderColor: "green.500", shadow: "0 0 0 1px green.500" }}
                      fontSize="lg"
                      fontWeight="semibold"
                    />
                    {parseFloat(paymentAmount) > (selectedStudent.emiSummary?.total_remaining || 0) && 
                     selectedStudent.emiSummary?.total_remaining > 0 && (
                      <Alert status="warning" mt={3} borderRadius="xl" bg="orange.50" border="1px" borderColor="orange.200">
                        <AlertIcon color="orange.500" />
                        <VStack align="start" spacing={1}>
                          <Text fontWeight="bold" color="orange.700">
                            Extra Amount: ₹{(parseFloat(paymentAmount) - (selectedStudent.emiSummary?.total_remaining || 0)).toFixed(2)}
                          </Text>
                          <Text fontSize="sm" color="orange.600">
                            This extra amount will be recorded for future adjustments
                          </Text>
                        </VStack>
                      </Alert>
                    )}
                  </FormControl>
                </Box>

                {/* Payment Note */}
                <Box p="6" bg="white" borderRadius="xl" border="1px" borderColor="gray.200" shadow="sm">
                  <FormControl>
                    <Text fontWeight="bold" color="gray.800" mb="2">Payment Note (Optional)</Text>
                    <Textarea
                      placeholder="Add payment note..."
                      value={paymentNote}
                      onChange={(e) => setPaymentNote(e.target.value)}
                      rows={3}
                      borderRadius="xl"
                      border="2px"
                      borderColor="gray.200"
                      _focus={{ borderColor: "green.500", shadow: "0 0 0 1px green.500" }}
                      resize="none"
                    />
                  </FormControl>
                </Box>

                {/* Payment Summary */}
                <Box p="6" bg="gradient(to-r, green.50, blue.50)" borderRadius="xl" border="2px" borderColor="green.200">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="bold" color="gray.800">Total Payment:</Text>
                    <Text fontSize="2xl" fontWeight="bold" color="green.600">
                      ₹{paymentAmount || 0}
                    </Text>
                  </HStack>
                </Box>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter bg="gray.50" py="6">
            <HStack spacing="4" w="full" justify="end">
              <Button 
                variant="outline" 
                onClick={onPaymentClose}
                size="lg"
                borderRadius="xl"
                px="8"
              >
                Cancel
              </Button>
              <Button 
                colorScheme="green" 
                onClick={processPayment} 
                isLoading={loading}
                size="lg"
                borderRadius="xl"
                px="8"
                bg="gradient(to-r, green.500, blue.600)"
                _hover={{ bg: "gradient(to-r, green.600, blue.700)" }}
              >
                Process Payment
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Timeline Modal */}
      <Modal isOpen={isTimelineOpen} onClose={onTimelineClose} size="4xl">
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(10px)" />
        <ModalContent borderRadius="2xl" overflow="hidden" maxH="90vh">
          <ModalHeader bg="gradient(to-r, purple.500, blue.600)" color="white" py="6">
            <HStack>
              <Box w="3" h="3" bg="white" borderRadius="full" />
              <Text fontSize="xl" fontWeight="bold">
                Payment Timeline - {selectedStudent?.name}
              </Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton color="white" />
          <ModalBody p="8" overflowY="auto">
            {selectedStudent && (
              <VStack spacing="6" align="stretch">
                {/* Timeline Summary */}
                <Box p="6" bg="gray.50" borderRadius="xl">
                  <Grid templateColumns="repeat(4, 1fr)" gap="4">
                    <VStack>
                      <Text fontSize="xl" fontWeight="bold" color="blue.500">
                        ₹{selectedStudent.emiSummary?.total_paid || 0}
                      </Text>
                      <Text fontSize="sm" color="gray.600">Total Paid</Text>
                    </VStack>
                    <VStack>
                      <Text fontSize="xl" fontWeight="bold" color="orange.500">
                        ₹{selectedStudent.installments?.reduce((sum, emi) => {
                          return sum + (emi.paid_amount > emi.amount ? emi.paid_amount - emi.amount : 0);
                        }, 0) || 0}
                      </Text>
                      <Text fontSize="sm" color="gray.600">Extra Amount</Text>
                    </VStack>
                    <VStack>
                      <Text fontSize="xl" fontWeight="bold" color="red.500">
                        ₹{selectedStudent.emiSummary?.total_remaining || 0}
                      </Text>
                      <Text fontSize="sm" color="gray.600">Remaining</Text>
                    </VStack>
                    <VStack>
                      <Badge 
                        colorScheme={getStatusColor(selectedStudent)} 
                        variant="solid" 
                        px="4" 
                        py="2" 
                        borderRadius="full"
                        fontSize="sm"
                      >
                        {getStatusText(selectedStudent)}
                      </Badge>
                      <Text fontSize="sm" color="gray.600">Status</Text>
                    </VStack>
                  </Grid>
                </Box>

                {/* Payment Progress */}
                <Box p="6" bg="white" borderRadius="xl" border="1px" borderColor="gray.200">
                  <Text fontSize="md" fontWeight="bold" mb="4">Payment Progress</Text>
                  <Progress 
                    value={((selectedStudent.emiSummary?.total_paid || 0) / (selectedStudent.total_amount || 1)) * 100} 
                    colorScheme="green" 
                    size="lg" 
                    borderRadius="full"
                  />
                  <HStack justify="space-between" mt="2">
                    <Text fontSize="sm" color="gray.600">
                      {selectedStudent.emiSummary?.paid_installments || 0} of {selectedStudent.emiSummary?.total_installments || 0} EMIs completed
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      {Math.round(((selectedStudent.emiSummary?.total_paid || 0) / (selectedStudent.total_amount || 1)) * 100)}%
                    </Text>
                  </HStack>
                </Box>

                {/* Timeline Filter */}
                <HStack spacing="4" mb="4">
                  <FormControl maxW="200px">
                    <Select value={timelineFilter} onChange={(e) => setTimelineFilter(e.target.value)}>
                      <option value="active">Active Only</option>
                      <option value="all">All Transactions</option>
                      <option value="historical">Historical Only</option>
                    </Select>
                  </FormControl>
                </HStack>

                {/* Timeline Table */}
                <Box bg="white" borderRadius="xl" border="1px" borderColor="gray.200" overflow="hidden">
                  <Box p="4" bg="gray.100" borderBottom="1px" borderColor="gray.200">
                    <Text fontSize="md" fontWeight="bold">Payment History</Text>
                  </Box>
                  <Table variant="simple" size="sm">
                    <Thead>
                      <Tr>
                        <Th>Date/Time</Th>
                        <Th>Type</Th>
                        <Th>Amount</Th>
                        <Th>Status</Th>
                        <Th>Reason</Th>
                        <Th>Notes</Th>
                        <Th>Actions</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {paymentTimeline
                        .filter(payment => {
                          if (timelineFilter === 'active') return payment.is_active !== false;
                          if (timelineFilter === 'historical') return payment.is_historical === true;
                          return true;
                        })
                        .map((payment, index) => (
                          <Tr key={payment._id || index}>
                            <Td>
                              <VStack align="start" spacing="0">
                                <Text fontSize="sm" fontWeight="semibold">
                                  {new Date(payment.date).toLocaleDateString()}
                                </Text>
                                <Text fontSize="xs" color="gray.500">
                                  {new Date(payment.date).toLocaleTimeString()}
                                </Text>
                              </VStack>
                            </Td>
                            <Td>
                              <Badge colorScheme="green" variant="solid">
                                PAYMENT
                              </Badge>
                            </Td>
                            <Td>
                              <Text fontWeight="bold" color="green.500">
                                ₹{payment.amount}
                              </Text>
                            </Td>
                            <Td>
                              <Badge colorScheme="green" variant="solid">
                                PAID
                              </Badge>
                            </Td>
                            <Td>
                              <Text fontSize="sm" color="gray.600">
                                {payment.method || '-'}
                              </Text>
                            </Td>
                            <Td>
                              <Text fontSize="sm" noOfLines={2}>
                                {payment.notes || '-'}
                              </Text>
                            </Td>
                            <Td>
                              <Button
                                size="xs"
                                colorScheme="red"
                                onClick={() => {
                                  setDeleteTimelinePaymentId(payment._id || payment.id);
                                  onDeleteTimelineOpen();
                                }}
                              >
                                Delete
                              </Button>
                            </Td>
                          </Tr>
                        ))}
                    </Tbody>
                  </Table>
                </Box>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter bg="gray.50">
            <Button onClick={onTimelineClose} size="lg" borderRadius="xl">
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Refund Modal */}
      <Modal isOpen={isRefundOpen} onClose={onRefundClose} size="xl">
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(10px)" />
        <ModalContent borderRadius="2xl" overflow="hidden">
          <ModalHeader bg="red.500" color="white" py="6">
            <Text fontSize="xl" fontWeight="bold">Process Refund</Text>
          </ModalHeader>
          <ModalCloseButton color="white" />
          <ModalBody p="8">
            {selectedStudent && (
              <VStack spacing="6" align="stretch">
                <Box p="6" bg="gray.50" borderRadius="xl">
                  <Text fontSize="lg" fontWeight="bold">{selectedStudent.student_id?.name}</Text>
                  <Text color="gray.600">Total Paid: ₹{selectedStudent.emiSummary?.total_paid || 0}</Text>
                </Box>
                <FormControl>
                  <Text fontWeight="bold" mb="2">Refund Amount</Text>
                  <Input
                    type="number"
                    placeholder="Enter refund amount"
                    value={refundAmount}
                    onChange={(e) => setRefundAmount(e.target.value)}
                    size="lg"
                  />
                </FormControl>
                <FormControl>
                  <Text fontWeight="bold" mb="2">Refund Reason</Text>
                  <Textarea
                    placeholder="Enter refund reason"
                    value={refundReason}
                    onChange={(e) => setRefundReason(e.target.value)}
                    rows={3}
                  />
                </FormControl>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <HStack spacing="4">
              <Button variant="outline" onClick={onRefundClose}>Cancel</Button>
              <Button colorScheme="red" onClick={processRefund} isLoading={loading}>Process Refund</Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Adjustment Modal */}
      <Modal isOpen={isAdjustmentOpen} onClose={onAdjustmentClose} size="xl">
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(10px)" />
        <ModalContent borderRadius="2xl" overflow="hidden">
          <ModalHeader bg="blue.500" color="white" py="6">
            <Text fontSize="xl" fontWeight="bold">Process Adjustment</Text>
          </ModalHeader>
          <ModalCloseButton color="white" />
          <ModalBody p="8">
            {selectedStudent && (
              <VStack spacing="6" align="stretch">
                <Box p="6" bg="gray.50" borderRadius="xl">
                  <Text fontSize="lg" fontWeight="bold">{selectedStudent.student_id?.name}</Text>
                  <Text color="gray.600">Current Balance: ₹{selectedStudent.emiSummary?.total_remaining || 0}</Text>
                </Box>
                <FormControl>
                  <Text fontWeight="bold" mb="2">Adjustment Type</Text>
                  <Select value={adjustmentType} onChange={(e) => setAdjustmentType(e.target.value)}>
                    <option value="credit">Credit (Reduce Balance)</option>
                    <option value="debit">Debit (Increase Balance)</option>
                  </Select>
                </FormControl>
                <FormControl>
                  <Text fontWeight="bold" mb="2">Adjustment Amount</Text>
                  <Input
                    type="number"
                    placeholder="Enter adjustment amount"
                    value={adjustmentAmount}
                    onChange={(e) => setAdjustmentAmount(e.target.value)}
                    size="lg"
                  />
                </FormControl>
                <FormControl>
                  <Text fontWeight="bold" mb="2">Adjustment Reason</Text>
                  <Textarea
                    placeholder="Enter adjustment reason"
                    value={adjustmentReason}
                    onChange={(e) => setAdjustmentReason(e.target.value)}
                    rows={3}
                  />
                </FormControl>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <HStack spacing="4">
              <Button variant="outline" onClick={onAdjustmentClose}>Cancel</Button>
              <Button colorScheme="blue" onClick={processAdjustment} isLoading={loading}>Process Adjustment</Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Cumulative Modal */}
      <Modal isOpen={isCumulativeOpen} onClose={onCumulativeClose} size="4xl">
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(10px)" />
        <ModalContent borderRadius="2xl" overflow="hidden" maxH="90vh">
          <ModalHeader bg="gradient(to-r, purple.500, pink.600)" color="white" py="6">
            <HStack>
              <Box w="3" h="3" bg="white" borderRadius="full" />
              <Text fontSize="xl" fontWeight="bold">
                Cumulative Payment History - {selectedStudent?.student_id?.name}
              </Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton color="white" />
          <ModalBody p="8" overflowY="auto">
            {selectedStudent && (
              <VStack spacing="6" align="stretch">
                <Box p="6" bg="gray.50" borderRadius="xl">
                  <Text fontSize="md" fontWeight="bold" mb="4">Cumulative Payment Progression</Text>
                  <Text fontSize="sm" color="gray.600">
                    Shows running total amounts: 20 → 80 → 160 → 310 → 500
                  </Text>
                </Box>

                <Box bg="white" borderRadius="xl" border="1px" borderColor="gray.200" overflow="hidden">
                  <Box p="4" bg="purple.50" borderBottom="1px" borderColor="gray.200">
                    <Text fontSize="md" fontWeight="bold">Cumulative Payment Timeline</Text>
                  </Box>
                  <Table variant="simple" size="sm">
                    <Thead>
                      <Tr>
                        <Th>Date/Time</Th>
                        <Th>Type</Th>
                        <Th>Individual Amount</Th>
                        <Th>Cumulative Amount</Th>
                        <Th>Status</Th>
                        <Th>Notes</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {cumulativeData.map((payment, index) => (
                        <Tr key={payment._id || index}>
                          <Td>
                            <VStack align="start" spacing="0">
                              <Text fontSize="sm" fontWeight="semibold">
                                {new Date(payment.createdAt).toLocaleDateString()}
                              </Text>
                              <Text fontSize="xs" color="gray.500">
                                {new Date(payment.createdAt).toLocaleTimeString()}
                              </Text>
                            </VStack>
                          </Td>
                          <Td>
                            <Badge colorScheme="green" variant="solid">
                              PAYMENT
                            </Badge>
                          </Td>
                          <Td>
                            <Text fontWeight="bold" color="blue.500">
                              ₹{payment.amount}
                            </Text>
                          </Td>
                          <Td>
                            <Text fontWeight="bold" color="purple.500" fontSize="lg">
                              ₹{payment.displayAmount || payment.cumulativeAmount}
                            </Text>
                          </Td>
                          <Td>
                            <Badge colorScheme="green" variant="solid">
                              PAID
                            </Badge>
                          </Td>
                          <Td>
                            <Text fontSize="sm" noOfLines={2}>
                              {payment.notes || '-'}
                            </Text>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter bg="gray.50">
            <Button onClick={onCumulativeClose} size="lg" borderRadius="xl">
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Actual Payments Modal */}
      <Modal isOpen={isActualPaymentsOpen} onClose={onActualPaymentsClose} size="4xl">
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(10px)" />
        <ModalContent borderRadius="2xl" overflow="hidden" maxH="90vh">
          <ModalHeader bg="gradient(to-r, green.500, teal.600)" color="white" py="6">
            <HStack>
              <Box w="3" h="3" bg="white" borderRadius="full" />
              <Text fontSize="xl" fontWeight="bold">
                Actual Payments - {selectedStudent?.student_id?.name}
              </Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton color="white" />
          <ModalBody p="8" overflowY="auto">
            {selectedStudent && (
              <VStack spacing="6" align="stretch">
                <Box p="6" bg="gray.50" borderRadius="xl">
                  <Text fontSize="md" fontWeight="bold" mb="4">Payment Records</Text>
                  <Text fontSize="sm" color="gray.600">
                    Individual payment transactions with amount, notes, date, and method
                  </Text>
                </Box>

                <Box bg="white" borderRadius="xl" border="1px" borderColor="gray.200" overflow="hidden">
                  <Box p="4" bg="green.50" borderBottom="1px" borderColor="gray.200">
                    <Text fontSize="md" fontWeight="bold">Payment History</Text>
                  </Box>
                  <Table variant="simple" size="sm">
                    <Thead>
                      <Tr>
                        <Th>Date/Time</Th>
                        <Th>Amount</Th>
                        <Th>Method</Th>
                        <Th>Notes</Th>
                        <Th>Status</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {actualPayments
                        .sort((a, b) => {
                          const dateA = new Date(a.createdAt || a.date);
                          const dateB = new Date(b.createdAt || b.date);
                          // Handle invalid dates by putting them at the end
                          if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
                          if (isNaN(dateA.getTime())) return 1;
                          if (isNaN(dateB.getTime())) return -1;
                          return dateB - dateA;
                        })
                        .map((payment, index) => {
                          const isInactive = payment.is_active === false && payment.is_historical === true;
                          return (
                            <Tr
                              key={payment._id || index}
                              bg={isInactive ? "red.50" : "transparent"}
                              opacity={isInactive ? 0.7 : 1}
                              textDecoration={isInactive ? "line-through" : "none"}
                              color={isInactive ? "red.600" : "inherit"}
                            >
                              <Td>
                                <VStack align="start" spacing="0">
                                  <Text fontSize="sm" fontWeight="semibold">
                                    {(() => {
                                      const dateValue = payment.createdAt || payment.date;
                                      if (!dateValue) return 'N/A';
                                      const date = new Date(dateValue);
                                      return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleDateString();
                                    })()}
                                  </Text>
                                  <Text fontSize="xs" color="gray.500">
                                    {(() => {
                                      const dateValue = payment.createdAt || payment.date;
                                      if (!dateValue) return '';
                                      const date = new Date(dateValue);
                                      return isNaN(date.getTime()) ? '' : date.toLocaleTimeString();
                                    })()}
                                  </Text>
                                </VStack>
                              </Td>
                              <Td>
                                <Text
                                  fontWeight="bold"
                                  color={isInactive ? "red.500" : "green.500"}
                                  fontSize="lg"
                                  textDecoration={isInactive ? "line-through" : "none"}
                                >
                                  ₹{payment.amount}
                                </Text>
                              </Td>
                              <Td>
                                <Badge
                                  colorScheme={isInactive ? "red" : "blue"}
                                  variant={isInactive ? "solid" : "outline"}
                                  size="sm"
                                >
                                  {payment.payment_method || payment.method || 'Cash'}
                                </Badge>
                              </Td>
                              <Td>
                                <Text
                                  fontSize="xs"
                                  noOfLines={1}
                                  textDecoration={isInactive ? "line-through" : "none"}
                                >
                                  {payment.notes || '-'}
                                </Text>
                              </Td>
                              <Td>
                                {isInactive ? (
                                  <Badge colorScheme="red" variant="solid" size="sm">
                                    DELETED
                                  </Badge>
                                ) : (
                                  <Badge colorScheme="green" variant="solid" size="sm">
                                    ACTIVE
                                  </Badge>
                                )}
                              </Td>
                            </Tr>
                          );
                        })}
                    </Tbody>
                  </Table>
                </Box>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter bg="gray.50">
            <Button onClick={onActualPaymentsClose} size="lg" borderRadius="xl">
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Timeline Payment Confirmation */}
      <AlertDialog
        isOpen={isDeleteTimelineOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteTimelineClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Delete Payment
            </AlertDialogHeader>
            <AlertDialogBody>
              Are you sure you want to delete this payment from the timeline? This action cannot be undone.
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteTimelineClose}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={handleDeleteTimelinePayment} ml={3} isLoading={loading}>
                Delete
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
}